{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["0ascctt7Uy6GJGFyuu79k7D9GGKIM1Y9XLAG5CIcekY=", "6qDPO663+Y+FXR4mRiJj5xYVrCP/IWDk7lywnSS3J2c=", "COYdMZU6pWOQdKbQRJAifoIbdXzx8SWj5+W8DXBe6Iw=", "QdRazrmVuEAKBOgRZmOdDEQ5/bodbDMNA/s5ytpoXkg=", "u0GnTlvyNUL5ywfJ+TsRLKZ0jiiIZY5m7H+HviynMkY=", "X7561P+Kkhf3cbXr3aBFuRzfPw75cNb4OpwT3XMTK9c=", "gSvU1oFofOrL8xAEYntZSApNyBMsm81jWo3r3z2m/bw=", "G4mg6yqRD0dny0l7uN8nymIsyGw7u7xqMokKAdTBHb4=", "UHLSuNxdB4m1rDocucFL4XO+UAzaEHdsuxGNIVybdVo=", "0RsN9tDRj40UhvZOHmOi3Dg5uHj213yzg7qXtZM10QY=", "mVNnrF/c75gdBRL7SnK8aT1X7dJFqYklIDDvqxZFi3o=", "lMUqLg00lNIikyOVJZglYRc5gXfxDf2+MpFMjuboec8=", "bpupwFldCaWFI714WOXh9ERPKUo0OgODlxl5pQyyxEM=", "KTADg8D90IHlMNnCigzXlFPvyclXuF0yyTzfn+1nbvk=", "UHEmE+AF0EMh9fmIkhG0hDZaldvbqO927vL3rnH14Ik=", "/UyDMs14ZDiqWbP7H8Qn/tE88QnMAvs5p/HYDZKDkDo=", "2gYYM1+Gv+E/M4PnWDJTa6lB0HRjRPrhltTDNTqsFwQ=", "H/QfWjiZ4mVS+yfmCbSW+ZsA5ld7n3PcQ3lLbaJMwII=", "ADElhd4rvkE5UACsyJ/HVc0NI4T/1PdnldmRma2MlMc=", "bOJEX7GUt86DdCx53Qm8p4uGulNUAByCbmMSMEet1U4=", "0tMAItP+dsGLUBYG/Foj9XO45N1QRLSaJ5kQpVvW4LU=", "82Lnx75H/dGlkmP4vSNakoIThb1/0sZ5WI3AH8j/w9g=", "wHLa20R63XTA0EwI41uZ2AdFwpciFTDgSGPOPfpMd6U=", "xqW3DFyGwOrbI5AU7SO+BE7R7kh23H4/cl6beHs13gA=", "rjqlSBqrLdYfD752GR+DTJQY1HIxJ5FCS1bNQDFSjR0=", "G53whdUYKT5aFP108Fc4VIQfJ+BoJ0opd4ljXk3gCho=", "CBNIWPNI9hXrpG6pUWOhjWeXsqw44vmOIeuW+CUNJ9o=", "U53z80ItxmiFcRLAZ9qFOdc28DTgHhksMcycs7rVlQ4=", "570S8XUCkr3GBPkbbiB70c2VJdJeOKr7BrJH1Lx239I=", "4zBWMwxch0I58xDhjzdvWLJevZEtZ/kEoaqpt+gdaCw=", "jiBwfHdxGjz8Qof17qwTsO4JulLVLPoshmXLy0zMh+g=", "L2uD4qvZySXXgxq3Nvlr9nrqyU/hl5VGzQbNYImltU0=", "ndtXxQnHRcr0QsVU8wneGyGWG4JeTYTFA/SeK1MtfV0=", "dDAV5jBqoGuXYlUlxQP0L2VjJ4QQML60K0jXTeRVNl8=", "ph4QQ7TH+N3z7GTN3ZiJVxHVxYU+cW5cjgMKCVVu72k=", "DhJCVl4nws95q01aMnNTu1wZSvxVKohgyhizHyEtzCQ=", "Qv9L8G+S2GTrjXsxAgVWoUxc9aXlB7i4z/QBASiAf94=", "kTXpvUPP0hIVU1OMY7sap8Cj9rQhBBPFstGYxcluaRU=", "rrTrL6Ul1aQtWbFciJt1Rr4b7R6gdfFCZKV/9fK1YoE=", "BSQeBFC1T/B2Z6mnlDbDm3PD76eFIRk/3IwLYrVch8M=", "h2Ugu5mekg5/vsKeUk3G3dD/pCUNMHBcWXfzl02msW0=", "wbAJCsHK5FEdaPy8Rt20IciNRswv9lHhmdFoklFdI9E=", "QT3yZjBTV/2BkIdc3pmVFZrhkElj5xRvRGJ8/yyUmhA=", "AGUapJjO1ZJUxNQEwOBOXHFF2v41fyFN1axataRSfyc=", "xr01euIXr6ZDbWeQCYXXAgdcs+mCkFX4hvwXt4yS+Nk=", "BnRNu8iFtsPgdREGqIojQgjeJE8nt0IaUBaQuF/7t1c=", "gKmUaTJPzQ+/hCz8Hxi04dydwhYjVDtcuHRxtSKOQXQ=", "LQFT/JeXdkFJwFzub70gBl0jINmNDCfY56COztgnn70=", "jDLHQ/KW/O7b/NNXTJrSVBahjA/pXRKSApn2n2R4GNE=", "cTXgYH4zF/F+7KL5uctY6QMN97IOBjQlOcBUCNpSIeQ=", "YBznBpoglnBhafKSfzSBgSijOmYrsRwgjdzV3CAirKc=", "h+jQV8d9SNWS1YQBzC4bR3WsIyHFD65fvlkU5DPgb0w=", "kuT0svERoeAoGD8fzXgQ1bCf8IkwGYY9p4mRERKFfKg=", "5lfxr6o2ZxtZdJzgCQD8dnbZtSLrm8+LsIz8GDBrA5s=", "HQtf0KX7N6bd+j01XMWSrBtU7swCoXVmgR5Kwmf5III=", "WPjbrrgBz2rIiEPMzrUZEb9VCN6kbczWy164EwdCFYs=", "XF8qvQaCMkmowm7YhVp6nrqxCO6vm0u/jCJMV6EPD+8=", "0mj3HvYX5HFUftQeFasF7IecZLmwjFuRoI2qMQiCIgc=", "oYsmOGaVkLHkxZYkayM8azrBrKYc5PJqMkrxvE2XpN0=", "HXYoa9dW1ugA7gkr5C4F8zi1T9bEScBhKwIX1d4/qg0=", "+siPgZ2x9luWolpmIJdLUyLyYUuo+Uqi3fecuhsCgAM=", "39giKNKS6gTMC30uAbXuYSy7/ZOePHW29sVPjK1mK3o=", "NSczrieEI7Lyu+PaUjBTfcU0rP1lAg0tG+1fmdhSdfc=", "YjDZQzWPrWE49nkK+XXQygHda6ug8p9rh/fA38Tewe0=", "Y7EIcpWyUCvLizt9+f6qTiutGRGVUg6IgPpTGmHxLuA=", "OCQ7dv470gNVtLTgtKjwvb2p9MjHJwa9xtmD84Mn3DA=", "HjOlaAosmA8liU4YZ6BxpVbbnCrxSVH12/g/+MEu/go=", "Kxk1iBMO7COuTgQySAhbE8/QJjyvkp/35ly1ln+4EdY=", "MUsov/AoWaJMH8bOj8wQlyM6hAhhtJkJRxE+x8y6q8M=", "ENKseWtjnCZ2KRLRayqOBnjr68cFDhMpOqflwoXCe4w=", "Yu1Q6DQzhlg7ZvSVGVRxiE9a+OMYAqTBkom0ENX5hYI=", "TWUbOM2b3zUQIGxnn6WaLrNWtO4bBLGl2e0zRlOW2uE=", "mfnu8N7GJoW0ALlFcqPJS+29c3YZ9bNqFMvcmX2iFCA=", "lKDjZrNEptVFqVPHN7hWFugOEMMHw6TLwmPZe5Q/PSs=", "+mJk9dUpYdum7DhQ1SJJMF0xO+gY5CfH1kqhRW+pNCo=", "XbpKyi+6MGkEu+XPShsMI2CFUKjvKVGERL+3j0kTBVE=", "f00S106Y82V+RXzsL6lSV0hITvZD6DnPSdgkxYUpPbk=", "Hw9M8GdkHQvaQR0zd492d197nC0cC/OjNBRDgzrUhZw=", "KAaWi8GvjLL4SIIfzDg5OjTK2uN2/ALfT6grcwtH/zY=", "vzoIs+JFr72YSgjtQsrHKs6Hd4WTmmy6xcko9QQ+beI=", "BoUFkU9ixhm7q+AH2/5i6emJdOuFQbrUgXQBelT/9lQ=", "wjihaoZUXsLrIGO3QjnnvT1p6VIST27yYvhfoIrYw7E=", "IjsyiAjh9kE2Il5+cTTqe8COSFbDoQH3zmFJWhSDzqU=", "ln0wf84l7x+ef2gixlqpBX+YjYG5Bc2Pj0zOmzxetwA=", "liijwWnRyvS8xF7qCuA2nae4lDXOfa1GbKPJ8mPg758=", "DqMoFG5ynMrf2A/3C/5vD1zG9rOVvSN6uHhA7jMwjnw=", "Wse2beg0SVp8plzowDjm9KwZQyW7XwDAfyPbe6RA5Es=", "ONxUUsIePeUAzLUpm8q/Gp3BroHyXh9FHtzvxebtczg=", "FoHZyPRzXOEaqPe3cyejHxEFrDu3Ydy6xX1qndb6j2Y=", "mDTLbXiHHsCIunY00/3GXdseOj0Qu6hxqQuD5zzMNzI=", "GvlsNpdsxGrpO66/ZTY+vS2TiSuDyGCrmCXf3uYva5I=", "v4Bb1+MB0SuamzaLFxokbWBFJ2kYKig+5Syu9E5H+9g=", "Yt6vdnpiZpJwvb59ZsJLjhyInZrslV4X+UEvVIlzdyI=", "kXo4/BWhspnlzfA2tZw4YiQZsFAWS8NPIgT1KYhIYLM=", "yaBN6FjqN6KDl7LLloVx3w2mPvmwt9sMONWQvJqrOBg=", "xl2N1XzBqIz/ByFZshROtA12+WJTL4CkBoh40tVr8B0=", "9mZdzMJpjT7fGS/iE+MvWDIyaSgNB7sJzBQp2Iv3x8A=", "aiFcU8N4Q0yo+o586DWXK+JqhBCfHqjaMHv4fCMFQ+0=", "80NxCiFlALn6GpHOO4Kh4sCLE4+HxxX2H29VBbZ4z9Q=", "w9KSshSzPTyS4rw9cZ0cfUGdeobCEUDsCPVjRferlR4=", "c1iVofB5cstgqzAnSsxL6zNVl/JDPQQVvbg/Ttj4pjI=", "rGLbwNrNfZ9N8GCt+5mV+htMW4NSJ2uyeXEfI1s0Rhg=", "zR0nSpZj+7q9j5w+8weg8LAyet+YDSmB7bQBxEAjDmU=", "raMKAQOlPdxlOF91cH+JDAnzM0u7Vv8wVT9BUkIVJPE=", "6obNe9eJ+3VUHUOGzq4MFk6lT1J+d/y5EjnHwCKeErU=", "jBuGNOd4IIYhI2IBguwURQxTVNa8RPObglAhzfSwgFo=", "sExR015HdrJIHCF91atJp8oQhZTL3VlnMOOMsuxp3yk=", "knGPp6lvB24+aUWIJ9KMgFDE9bx/Aso/HaYUk6Tc2pw=", "XkaClwI3euGmg5c16F9hsC/nDy0MFnlar4UOwMo3g4Q=", "//xzVrcRMv+IUAabv4NxFB1mBLwFfdcrjLZC+BH1I30=", "iB5BXM4Adu+Fc/2onGyXF5hksjXTFNYpd9TcJUzwZh4=", "zOi4rKE2T7e9OiVXu2qwSDcRyvcI1RojS/MY0biTHWk=", "eFhIoaBxvA2oYYOW8MRAc7D0GxCOgqXMKC0DseNp3L0=", "Ac/W1MU7iG1n2vQRVD75ieA/WT8f3w9EKFLFKx1wPzM=", "wiPUMeKW95OYA0z5NkE8UsiKNxl03KRBJ284bH80Y4Y=", "IdZK7NxXjrUCbSpYhELhHqd6p4gyIQVHjoU3SCYRfeg=", "WCdIE1lZCkQupHlLSqiwoLF7XeYEJM5v0wlDtPLWVmM=", "Zmf22dR1/sfySG+8zz+4nw1XtQFleANGaVEu+nezo3Y=", "P9QSmYrFSl2tAanwhoDtKKXHmZKDDqgxLuFEFY1M/3E=", "v5HxpD/jYnj4/PfdMSX4v53EGRFb92+yHfD2WhHXZUc=", "qmc3lU5cMy9Y+nb66g4soP/TtcEH2UfG9SwqO6yr+Cs=", "AeL9EfhXT431k2+C4aCes0WzeqkOgPA33UTVSzotlcg=", "Za4OmlwQVGR1bl3lPw26Vii6XJXUpKQPN/RK+gndw3w=", "c8ZMDyWOvCmlWqSquXSkLueBGAnIKo/k5GnXBYbPwwI=", "M2/0gwR4YHH68X48NrVBJR9qzdsLIkCR7xnrDfuJfzg=", "xqjVKkJdbu88C91LyPqimVlyt7bKT9ZBeywBjQC/nKc=", "nP+Ah8mlISpMbbTU3xRvwveq7zElQHpK/2x52+GgTwY=", "kH9hYautY3RzEPSfiUmZfMmg41BNSUm/lq1007hFbY0=", "EPyweL3usrHyGxNyJxIGd5Be+T7XNC+ZzvHhpVGi5Xg=", "K3H9thCyscCUfiQ7RbAr8auFO87y0CrD8wocyWzC8Wk=", "ZAPPoZkkW6Oj2vBkYesE9UNAEDKdR5IvnV2BJQy/dfg=", "oodC/pS1i7Adex0rRI9FMvKGEUywJted55lqLZTFQQc=", "Q3Vslh2aJOrVih4LMDTp7e18wEjDwORoEBxmZ50QLBk=", "7NQqW42pxv0FeDYDcpLbcQC1l4W0BjoSvhWYWWYhS7U=", "zY67c6cdhnkqX0tJMfmbXB28zNPfVc8kR2Fc2yti0as=", "LxmU90NGokVcJDuZSVUr+2oKM75uLBl48DrAHL9Hifo=", "VXNSqZP658rFO+ulQlJENfNIoy2k52y9zIcvRgzoV6w=", "fRqhmc2LglkbWGojA4UA+yQKRZc6/VbMigDWq42Bkhg=", "B9WUHcoj2E/pEujS7o2V0lg1D9FVruksK0D8SQhYvLI=", "xKkClWPXMWDTnK4ZWW2BH8e0z4+r4Mt5Uhr9gWe3pCc=", "JI3YURGN3mOJr6iIKE1Poovf5uIGSgpr/lOk4zUki6k=", "L0qNvKTEfZuEU08x6Eixi3rb8O66q/HwMHwqpY/bF6o=", "ZqEM8jYUDYMfcGRx5xsUwCkLmxScbsuj7lDMgf3DGdA=", "LXhGSVg00LjJyQa89+YjJUBFV7QNa72ok4GkoyapCf4=", "guewnpHR1ASfEyZkVRaIfB2hwQBxbgV8EsLuwx5MSYU=", "bdPq8o3kbGWUoFUfBnKW3mFCCBGMPVgGhD2Fdt1J5FU=", "Xx+ewYi90AgI4AYn+cfSoZwmngeh5Y7OxMVTa7sQ1VE=", "RpFpmszy9RnApPqRoh75n1weAoQwnI9dOTsHBRAI7Do=", "AJUssMAVbDjgJU+4TTGmcYsk7ATcquhHhBjgVRJNHgI=", "K5yxLPlPLSC7BwpXP4V4sajf7seWcTsT/2cRjRKDKOg=", "z36cr47my+0gHKUYFUhsPFO6O/IwJXOdX1kmp9mbjk4=", "1Boz2aJeE0ymxCLm3Mv/Zm1gR9w9WfeXDwKyBzDiceo=", "issUEuQYugvxcrY8mR+Q3dNokoYaq7nPw/yDFM80hHE=", "OdpX+DwKwxzV4jP80QK5s8KT5mQ2rHpHuV4Uz4fdBwo=", "Fh3bbl0ftXkAmW0WXonnU+QOQouQgoHb6+a93R+pqGs=", "WKHFA7oOIIMedmAcCMdQEcc63MY39W8E3zTQalPw+2o=", "I5pdWcYQ8aVNefz0Kj+4qnfqUgADWXDN6AP5iLTNcS4=", "LXA3f87QAwvnX9cFFg1eayVyOk0V9dKYZsvI6Si78tM=", "usNui9cTua3F6ZfUihbYo60f2iNJRqFEU+JgMAebVCY=", "A1o2pKU+jUc345OVDWCMOi0zEmzea0TY5sl7SVFXKPQ=", "Mt7t0qL1WuZse01QvsxhzDsCuSGKqrb/F4kboIO09wI=", "O/yKYm4ZJhDj2EBOt8IMxmYWL8izfxXKhFlKLHxSwj4=", "0SyC9h9yGbYsPoqdXx11pUiLOXMwHWAfNUXk9TZ/NDU=", "w5m5/1JmXD311NIX3WwALE14KKjv6KKfX0vXCJk96mM=", "zwgCo4QKifWYIVvQTZoQdZBLVyTw0fuB7M4Aacz1Rak=", "Z88Y40YL8m7qLriAj2d5kEaP7h1bEKmcLCTIElSBFtE=", "gI+9381WYNfKEI8yLXqaM2gBroHfeqc8n/1XOnWfz4A=", "kn6yXl2pnC/zebdgbOy/mZbR1r6qwFD1NPPmDknKpr4=", "j6mYLp7quJEKxLK0gYoLyB//eKZb1hu4QRvqrirqnqE=", "ZuCzgJJLRfyIReJsab+8hVJ5T9axt2bGGtAC8J44HYI=", "a9DB1WHg12KQuco/5lau7WpnShkBiujWzZKMu+C4WJY=", "jLfeECnvz+s3wL/4zBhVETZakjDaeaZPN6NYxkVVCrE=", "JGdyQ/A7/+alro198PWMQngiPGPu0E/z9WFKj3yhb7M=", "9ZpamW3qPzn3BGJrk6wnLIwoz/uRgdZ4Ga8E2dBwYBQ=", "5yZyusPP0zFnNB+u6F4A+5N+8wvUbmmDNwNAv7/FEOk=", "CowNy0RscahTWGH61S02XmpXmQDO8omOkE163xV3VYY=", "CiIQ2baxOWBcnGZmOqGvHP6ETSLuTlC1xLaZNKNaKaY=", "OHt55eY6ZWzZpbO1w7aGHXFyGl40cbyMhghHEsy2Zb4=", "7uxfjJh52zHdimSRH62gdK71MuGQ1zERh8EoBGJ5PeQ=", "Fn8hFL4zC7GQiLhpe67L/wdvVOj5vDGDzjBYOxxryEE=", "Hd+Ksw4De/T9ff6GJ0y67YR4GVSlnu9miTkhV30QDOc=", "O0YpQl+rAV+Eol6VNA3qLyjPvubdYavliCpo7KzbCC4=", "B2TePji/Vu/hQKXdeIY74yMKIUOxoLKEXewZfffq1Yc=", "CHMApOywvUhE0ENplbY87Su3oELBKWLaRhZD22hcpBw=", "Jcg7yPaDj9IzYB84AL3BScqvzIFvQdeth3jBqeTOX6Y=", "b8c4f9/QrvV8hB5HypSc9DiOzQG7o3JWPd1x47AzF/0=", "Fz9qDS4ObOtoVjy7q3nqo4trdXxtWbxZ8/6q8B64PTw=", "s23vQ5b8jOR1PO8yiqqF4tsCFR6NyQsO7Rf0VMA6gvU=", "ZhqKMZU3IjQzUsS/WTRMQHW5pgDxJwp0YM7Mg1VZbqU=", "Ul3F8d638IoXrB6rTrBhO8ykY0IkLuiAjouI+rXn1AQ=", "PT2oC/2DuthlrR5jerdCkCRYPKz/hOcBXcvacZh4c7g=", "nKsHw4PP/jzweORVF8hHlEWcqkAT0Oa2HjI+sikHx3A=", "aOi/yAFGNUnwSB0vDeNHJxjW1Szv0nDMhn3tTmfBBjw=", "OQXBycZA41Dd3MSw9krNfTQGNmDtKFm0D6bK0Vyjc5I=", "xKrHuY0vTtBBl8T5WcycpgdTSt/q9+UPNLecj3gV6x0=", "FlFzG6K74+ARIufehb1tA2Uh7ZiIJLVimE2snyvAMDA=", "AthBL+KGq/IXWkgfC/YNrUab47kvskrue4oU16LzETg=", "5pdlEZY+sEwkTHCnjjWyB25UlJnMj9s9QV0SE8zW868=", "rkUFmygSv0Gy8jd5cnLRhEIsfGz56zqvcpJURs8CTok=", "nt4xsLUOB66kQAdrnFx82Boa3S2lkoHafU/IdGu+d9w=", "TxaTb679yh52mPiPQBLiLkkM4CzbxClPzpluiyZnq4s=", "HS+Fkfz1TgkJJcWSxO1ijYuyzMU5VhObvSme3JCyfRU=", "2WM/gTRSyR94VHmefC3VekgKXN+PnHuSUPKoBYGtwGo=", "k2cry5f7hwtA/knIq967Mn6OxsqA0NJGK5hWZ2KgxeA=", "LOIXFOTEQIrTZJxsy8yOpdgSSwIOEOTrRSYy0boUjOY=", "IucR6FKsCF3c3yCdQOCIGKlNBT+snkR1woiU+yc1khI=", "+LVSNp/p8Yf/yRfDgOhztxf4Wep8xKFqQZarWzOpYNM=", "6/u1hQWRbChcrM+axq3USkqaIaR8Vt0oyUXCLV3c8Ko=", "e243bCS6t78vuU/E5meT9HaMR5wl11Rucf6X3bxbrPE=", "2jByFq2QDRuOhnmdf0BiaQnxTVR6FNLRRBAZoiBehtA=", "xK3dvjXg4OCLbJWKCHm7rob/bWou/PbCCFqcdHz14lI=", "vdKU4XuoduadVgQdWdDLn8v1+mcmjgn6quynK18cfXc=", "GR7u3hYCXfyfFwxt8G4icaOoIY/3vIVnw4i1+QtLdLA=", "caTuwPsCoXVtFK6pxk7MMCPQ4OwSxZ3DR5GoZUSQYS4=", "ABcYvqzRbE+DJeeyN8qm4E/tnyAXVvCmTzCxdPbjTmk=", "dE1GTDGovJGulf1CRwTF63shS89NfM7c4TVb28+fPws=", "VYMpvIS+7bHaPjMfcvlE40hcPTPWyZ1RzjDYTzJbTgo=", "OWAzEBpvXcXqzH7UXPH6hykQ3dERGdAf6nOhjOkCV9s=", "vNQH17RpV5YCndXpABDEAGp+f/yjiXsdvqE9NHWs12s=", "N87U3zP+nwi3DQL7z4biHZZtwl5PMCsY6+rUGKnpqQo=", "apklo1GnTDWzFqg0pQABRHD/Bl8wNy3qOpUHnBISSUA=", "hLR6gyFIXnlMplktSKu1TTcJUTAmUjFDxKsY6NNHKd8=", "zdiUflCVzXx0gmCSe/ZDEEFZNDXj1bZtT/APl8DwNQo=", "oH2k/YmznFNHjZ7/PpSC3dXl7dPK6C1+nseVVc9xVRA=", "Agrw7mwpI1tJrLxZUf9Vj5un+/xCEKQZ/EkyoaUEskI=", "aB1kaTyI64dHf5Lg46zsU5BT2WyqD4XUJYap1HJYdsI=", "s8EfIr8Zx2JWU+P/0MJDqDKx5nXXs11+F1Ryb1/WAmg=", "RTYq/SUq3UTJLfcwhGZKoNaP8OIzncYhwdgi8zMbH4A="], "CachedAssets": {"0ascctt7Uy6GJGFyuu79k7D9GGKIM1Y9XLAG5CIcekY=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\g9w5tdrjvt-otkjqv6u35.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ogo6wfeumt", "Integrity": "WN2Q/vZfoq5xRsWXt75TGakQiyzjVSx1P2f5h7JrLUQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\bootstrap.css", "FileLength": 476131, "LastWriteTime": "2025-07-02T05:37:25.6650235+00:00"}, "6qDPO663+Y+FXR4mRiJj5xYVrCP/IWDk7lywnSS3J2c=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\ev84ybuhfb-da1ikz1x3s.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\bootstrap-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rsjzt06b06", "Integrity": "BVQAu1KY3hXrrbby8EYqt+VMRD7avaj8h6tWvAvw5Fw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\bootstrap-dark.css", "FileLength": 475553, "LastWriteTime": "2025-07-02T05:37:25.7078065+00:00"}, "COYdMZU6pWOQdKbQRJAifoIbdXzx8SWj5+W8DXBe6Iw=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\ll0ck2c3pw-s7kmwdu5yf.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\bootstrap-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w3bq3jxvqs", "Integrity": "vJm+m8vnw6wPvg4T20NouyddMAylPZ+DGHvAtu96CQ8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\bootstrap-dark-lite.css", "FileLength": 475558, "LastWriteTime": "2025-07-02T05:37:26.0698073+00:00"}, "QdRazrmVuEAKBOgRZmOdDEQ5/bodbDMNA/s5ytpoXkg=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\m9pq0qobmp-cxbvj0zal6.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\bootstrap-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1zsz9whqk6", "Integrity": "bTaDH194c9dSr12ku1F9CrukPZJmcWM/yKkzZeu6dT4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\bootstrap-lite.css", "FileLength": 476137, "LastWriteTime": "2025-07-02T05:37:26.4497572+00:00"}, "u0GnTlvyNUL5ywfJ+TsRLKZ0jiiIZY5m7H+HviynMkY=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\g0kkhqlx3b-6ijb54a1ch.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap4.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\bootstrap4.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "50tpmzlxn4", "Integrity": "FTGE8d79zaBXHCdU6VOuo7r8fJYsmhDT2LANATOeFYw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\bootstrap4.css", "FileLength": 467065, "LastWriteTime": "2025-07-02T05:37:26.0806935+00:00"}, "X7561P+Kkhf3cbXr3aBFuRzfPw75cNb4OpwT3XMTK9c=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\fx6f6w99v8-0fg043zwgm.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap4-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\bootstrap4-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k328nl28v7", "Integrity": "oMedcRbE+j52uv1SxTvs60u8MzO11j56tHjXF0tYpuM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\bootstrap4-lite.css", "FileLength": 467069, "LastWriteTime": "2025-07-02T05:37:26.4040209+00:00"}, "gSvU1oFofOrL8xAEYntZSApNyBMsm81jWo3r3z2m/bw=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\czad4b1t5w-8bo2y8shfk.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap5.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\bootstrap5.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l7hzp1ncex", "Integrity": "4Q6MksKo0oFqdcP6Jk1CEJp3MWkLS8GWBP2PrtJd+No=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\bootstrap5.css", "FileLength": 474409, "LastWriteTime": "2025-07-02T05:37:26.2376232+00:00"}, "G4mg6yqRD0dny0l7uN8nymIsyGw7u7xqMokKAdTBHb4=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\w7wzav3zkk-c4u2p3fbmr.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap5-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\bootstrap5-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xc7iaby9ab", "Integrity": "wat4Gd4BTTZVoPa3w+I4lpRxiOFO6yzZm/Ne2Ngrki8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\bootstrap5-dark.css", "FileLength": 478261, "LastWriteTime": "2025-07-02T05:37:26.5585717+00:00"}, "UHLSuNxdB4m1rDocucFL4XO+UAzaEHdsuxGNIVybdVo=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\ezik3was9u-x2qei2lj68.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap5-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\bootstrap5-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rn41heff7o", "Integrity": "1yYYhDotmXsHiP8aXH3cWH4pZ9SQbLnh2ogTSObzmlo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\bootstrap5-dark-lite.css", "FileLength": 478266, "LastWriteTime": "2025-07-02T05:37:26.911928+00:00"}, "0RsN9tDRj40UhvZOHmOi3Dg5uHj213yzg7qXtZM10QY=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\0gmeqlood7-5n6pwg2q85.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap5-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\bootstrap5-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ssfyeumqpe", "Integrity": "fGpNq80OywoesvLUkEgDS94NEMbniBFJi3eTRIUn0fc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\bootstrap5-lite.css", "FileLength": 474415, "LastWriteTime": "2025-07-02T05:37:27.2919617+00:00"}, "mVNnrF/c75gdBRL7SnK8aT1X7dJFqYklIDDvqxZFi3o=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\rceg06wfiw-4tdxru61he.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap5.3.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\bootstrap5.3.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7n5581fzp1", "Integrity": "0wcd58/g5x5Aes4VMEwtqqPCEimj/50nCv31OHpRlf8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\bootstrap5.3.css", "FileLength": 480211, "LastWriteTime": "2025-07-02T05:37:27.6856302+00:00"}, "lMUqLg00lNIikyOVJZglYRc5gXfxDf2+MpFMjuboec8=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\5q2prff4m2-v9qfcxfxt0.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap5.3-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\bootstrap5.3-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "aefy8ko9dl", "Integrity": "juXqcXN75NnAZpp8a7Y1yM0Hhs3ZYsifMJ8axep+itg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\bootstrap5.3-dark.css", "FileLength": 479301, "LastWriteTime": "2025-07-02T05:37:28.0471221+00:00"}, "bpupwFldCaWFI714WOXh9ERPKUo0OgODlxl5pQyyxEM=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\awwokdaaq0-p9i9uyxbyk.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap5.3-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\bootstrap5.3-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2whlaspmz7", "Integrity": "0pdqSKyafudh6yX6L8YNMEHXpYQ9siFnx3Se/sqp3sE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\bootstrap5.3-dark-lite.css", "FileLength": 479304, "LastWriteTime": "2025-07-02T05:37:28.32153+00:00"}, "KTADg8D90IHlMNnCigzXlFPvyclXuF0yyTzfn+1nbvk=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\984b865z1b-0az1g3v0c8.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap5.3-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\bootstrap5.3-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wnadw814nc", "Integrity": "PMfxL/TWVG0oTPu9QBAP+7Se/fN4/JvE9iPhXwm8r6Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\bootstrap5.3-lite.css", "FileLength": 480217, "LastWriteTime": "2025-07-02T05:37:28.6448486+00:00"}, "UHEmE+AF0EMh9fmIkhG0hDZaldvbqO927vL3rnH14Ik=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\ytu8lus5q2-xesxqavj00.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "customized/material.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\customized\\material.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "idppmstdiq", "Integrity": "St3Xs4JsQSC3D6xUSdUuaLcvljgeoUucrrvQrrKaBoY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\customized\\material.css", "FileLength": 504107, "LastWriteTime": "2025-07-02T05:37:28.944066+00:00"}, "/UyDMs14ZDiqWbP7H8Qn/tE88QnMAvs5p/HYDZKDkDo=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\mpbuet7hpe-46u0nd3byl.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "customized/material-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\customized\\material-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9c1m8you53", "Integrity": "t39T7WBvfrCYGh1spgu9vG/0/YCMThQFLIhOjjdsStM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\customized\\material-dark.css", "FileLength": 510577, "LastWriteTime": "2025-07-02T05:37:29.2285643+00:00"}, "2gYYM1+Gv+E/M4PnWDJTa6lB0HRjRPrhltTDNTqsFwQ=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\pxx57cfmdu-4tekdlp3y2.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "customized/tailwind.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\customized\\tailwind.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xufn6cqw00", "Integrity": "9zkWHW4FNM/0XvhknducNZM5WEejH+JH5Cf6ygH2c+8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\customized\\tailwind.css", "FileLength": 438547, "LastWriteTime": "2025-07-02T05:37:29.4845462+00:00"}, "H/QfWjiZ4mVS+yfmCbSW+ZsA5ld7n3PcQ3lLbaJMwII=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\yu10ux9zg2-1kzk8d3a4y.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "customized/tailwind-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\customized\\tailwind-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yeqsaa54nk", "Integrity": "unaaPfySQAsmEEKtLz8iyd2TCuFbM5GUdYYWZmTs5hE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\customized\\tailwind-dark.css", "FileLength": 441526, "LastWriteTime": "2025-07-02T05:37:29.7429852+00:00"}, "ADElhd4rvkE5UACsyJ/HVc0NI4T/1PdnldmRma2MlMc=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\xj9e5awasb-sf1x19keag.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fabric.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\fabric.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "arr5fsvjhh", "Integrity": "xioVMcM2LuA5RZYYyzpMGLz+/NDEOAIMbuvh4lNjZJM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\fabric.css", "FileLength": 455590, "LastWriteTime": "2025-07-02T05:37:30.010502+00:00"}, "bOJEX7GUt86DdCx53Qm8p4uGulNUAByCbmMSMEet1U4=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\f1vaqyeoju-b26ojvrkef.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fabric-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\fabric-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "72dvrws58u", "Integrity": "ITEkKGtsgCG9Rz2Q4gCGm0r42rkrZ1Z7DRW7dS6//QU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\fabric-dark.css", "FileLength": 465562, "LastWriteTime": "2025-07-02T05:37:30.2658332+00:00"}, "0tMAItP+dsGLUBYG/Foj9XO45N1QRLSaJ5kQpVvW4LU=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\8fhnih39yd-ihq8qp3om2.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fabric-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\fabric-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8kimz81g9z", "Integrity": "iMXR8M1Prkzeg1EFemsHeSD7w+9Vid2dyAuMrRXggD8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\fabric-dark-lite.css", "FileLength": 465566, "LastWriteTime": "2025-07-02T05:37:30.5125586+00:00"}, "82Lnx75H/dGlkmP4vSNakoIThb1/0sZ5WI3AH8j/w9g=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\aqas0gzsh0-8xjryt75x9.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fabric-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\fabric-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7bvcujt56", "Integrity": "h4FuFpbeKgXu6/QtUXIDv/an0o6KKwkEOJk3as99aBQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\fabric-lite.css", "FileLength": 455596, "LastWriteTime": "2025-07-02T05:37:30.7570344+00:00"}, "wHLa20R63XTA0EwI41uZ2AdFwpciFTDgSGPOPfpMd6U=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\r5ssla993m-1836m79nrc.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\fluent.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "x3qeui7zc6", "Integrity": "rRHLPTHteN8hcRGw4C2x2tEO1wJ7IMFtc9WKSussdMQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\fluent.css", "FileLength": 454524, "LastWriteTime": "2025-07-02T05:37:26.726935+00:00"}, "xqW3DFyGwOrbI5AU7SO+BE7R7kh23H4/cl6beHs13gA=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\yqbsc2e4c2-5kzff4cv39.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\fluent-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qh7mq4ivx0", "Integrity": "kmZxaTX5Yf6GA2/qKFvTuu3ozWjsNHavWveXoMxwjPo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\fluent-dark.css", "FileLength": 454851, "LastWriteTime": "2025-07-02T05:37:27.0959936+00:00"}, "rjqlSBqrLdYfD752GR+DTJQY1HIxJ5FCS1bNQDFSjR0=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\2dszv1c0dc-xkjillxrw3.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\fluent-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5p2op5air7", "Integrity": "qTBVskmHxZ5IbFEoBa2dH5g4cHICpQTqgPHrgaA3Bas=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\fluent-dark-lite.css", "FileLength": 454851, "LastWriteTime": "2025-07-02T05:37:27.448177+00:00"}, "G53whdUYKT5aFP108Fc4VIQfJ+BoJ0opd4ljXk3gCho=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\ubxng02trl-92hisotj47.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\fluent-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e4g71acazq", "Integrity": "XpYnHbq0wwgSyro2YZMZ9kksp3vuTMULJbORRE9Rrzo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\fluent-lite.css", "FileLength": 454528, "LastWriteTime": "2025-07-02T05:37:27.8133082+00:00"}, "CBNIWPNI9hXrpG6pUWOhjWeXsqw44vmOIeuW+CUNJ9o=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\h56puxhs03-t5cex7guuf.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent2.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\fluent2.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "satf3egpmj", "Integrity": "7KJbiABRJKHbj+6faCQNtRODO4J2ZKWy/yqp07IOWbI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\fluent2.css", "FileLength": 490238, "LastWriteTime": "2025-07-02T05:37:26.8098602+00:00"}, "U53z80ItxmiFcRLAZ9qFOdc28DTgHhksMcycs7rVlQ4=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\oaindoncb5-2t9ms4tcmi.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent2-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\fluent2-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g3g686sjsu", "Integrity": "BAlW9qxtCOvuptcZ6/oyWzBBB4TnTXdmQ12pnePhHfE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\fluent2-dark.css", "FileLength": 487229, "LastWriteTime": "2025-07-02T05:37:27.2342559+00:00"}, "570S8XUCkr3GBPkbbiB70c2VJdJeOKr7BrJH1Lx239I=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\xhfo9ezgkd-xuetto8xig.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent2-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\fluent2-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5h9w3twvia", "Integrity": "+S5OLTR1CDtK22lwcLaOal1oPDyJgabdhGHerQ10WOY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\fluent2-dark-lite.css", "FileLength": 487231, "LastWriteTime": "2025-07-02T05:37:25.8339431+00:00"}, "4zBWMwxch0I58xDhjzdvWLJevZEtZ/kEoaqpt+gdaCw=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\1e4urs263g-g3i50uzo9q.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent2-highcontrast.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\fluent2-highcontrast.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ycdfast01d", "Integrity": "ngSBmTcyEcqNmv9HDgopKXSd29oedxqBrzz6Zpw5GBw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\fluent2-highcontrast.css", "FileLength": 488741, "LastWriteTime": "2025-07-02T05:37:26.2120033+00:00"}, "jiBwfHdxGjz8Qof17qwTsO4JulLVLPoshmXLy0zMh+g=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\4qsv9tgae5-3hymraaq3i.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent2-highcontrast-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\fluent2-highcontrast-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j2o6krkn5t", "Integrity": "pWDdY6wRoB7/rPxGR3J0kJbu/40dYcXne+p0FtI3JhQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\fluent2-highcontrast-lite.css", "FileLength": 488742, "LastWriteTime": "2025-07-02T05:37:26.5804524+00:00"}, "L2uD4qvZySXXgxq3Nvlr9nrqyU/hl5VGzQbNYImltU0=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\lf21o06s6y-rgdlv06ij8.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent2-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\fluent2-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9w70ue7zw7", "Integrity": "+S1ehTo4GyEZ8RsUbCMn+3nr2Xb9UeWQHftW8nA38Oc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\fluent2-lite.css", "FileLength": 490242, "LastWriteTime": "2025-07-02T05:37:26.9554564+00:00"}, "ndtXxQnHRcr0QsVU8wneGyGWG4JeTYTFA/SeK1MtfV0=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\8u73xo5u52-b4ocx4y4bv.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "highcontrast.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\highcontrast.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "awyfm8cgyd", "Integrity": "luusQZBKy/TSVBjvfP3vU9Rmg5IJzOP/zKszoDfuxjc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\highcontrast.css", "FileLength": 453528, "LastWriteTime": "2025-07-02T05:37:27.2322431+00:00"}, "dDAV5jBqoGuXYlUlxQP0L2VjJ4QQML60K0jXTeRVNl8=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\sa1475ed2m-10r141mz96.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "highcontrast-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\highcontrast-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ysh75zb1qh", "Integrity": "4taLR6wLBkQPKrYdprheawjy3xgjUQtw2uNM5ynV0u0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\highcontrast-lite.css", "FileLength": 453532, "LastWriteTime": "2025-07-02T05:37:27.5135096+00:00"}, "ph4QQ7TH+N3z7GTN3ZiJVxHVxYU+cW5cjgMKCVVu72k=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\w0orhpiaxo-x8d4ppitx9.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "material.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\material.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u7enn9r0ow", "Integrity": "Rm1TzprVW/Xqa/VP+oYiM5HjQZu2Vi2gdigpA3JWdTk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\material.css", "FileLength": 504157, "LastWriteTime": "2025-07-02T05:37:27.8767675+00:00"}, "DhJCVl4nws95q01aMnNTu1wZSvxVKohgyhizHyEtzCQ=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\4ulei2vwud-2hmf80gs3w.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "material-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\material-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "583lszzd9u", "Integrity": "lXuas+VHha0AHqPmagnukH8/6wgsUA7/+nHYOHTY62w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\material-dark.css", "FileLength": 510613, "LastWriteTime": "2025-07-02T05:37:27.6401379+00:00"}, "Qv9L8G+S2GTrjXsxAgVWoUxc9aXlB7i4z/QBASiAf94=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\ay4l9je90m-m79jryhlek.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "material-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\material-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7xe8yjprt4", "Integrity": "i9vNZPaxVtsL1vlkX9quN5TMqKyWYs8Qnr//ocUJdGM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\material-dark-lite.css", "FileLength": 510616, "LastWriteTime": "2025-07-02T05:37:28.0271451+00:00"}, "kTXpvUPP0hIVU1OMY7sap8Cj9rQhBBPFstGYxcluaRU=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\fsp2o9negp-wdk20m8wam.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "material-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\material-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gy7y5k9cs9", "Integrity": "yOHPfhtH+oMVwg7xf9d/O0nnJxLt+0qxW5g1zxTesHc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\material-lite.css", "FileLength": 504163, "LastWriteTime": "2025-07-02T05:37:28.3063462+00:00"}, "rrTrL6Ul1aQtWbFciJt1Rr4b7R6gdfFCZKV/9fK1YoE=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\s2yf70i8pu-69c71lqj5p.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "material3.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\material3.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xrzq5n1phm", "Integrity": "Y5c7vDuWo9GWF8Szu1ZK3apL4O7aUTnYHxcKAw2aazk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\material3.css", "FileLength": 456104, "LastWriteTime": "2025-07-02T05:37:28.5833811+00:00"}, "BSQeBFC1T/B2Z6mnlDbDm3PD76eFIRk/3IwLYrVch8M=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\wa7m2rhtxr-ectdqosaj6.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "material3-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\material3-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7qez458gkr", "Integrity": "sZJe0rwnycjpy+Fpk8Yo5yvJXG3wK2G+DBclAV/yMAk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\material3-dark.css", "FileLength": 455644, "LastWriteTime": "2025-07-02T05:37:28.8750654+00:00"}, "h2Ugu5mekg5/vsKeUk3G3dD/pCUNMHBcWXfzl02msW0=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\yjifzcnj40-twain4oq60.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "material3-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\material3-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wg000crjis", "Integrity": "YCInYPeplRZZ06JAt6R+NUc8BBxRrog0oJHRU3IUy3c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\material3-dark-lite.css", "FileLength": 455651, "LastWriteTime": "2025-07-02T05:37:29.165181+00:00"}, "wbAJCsHK5FEdaPy8Rt20IciNRswv9lHhmdFoklFdI9E=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\wkxfmls346-xvv2htanqf.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "material3-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\material3-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cujhx3k08y", "Integrity": "TJSogDWNAFLP0xFt1kEO+qca1w6qKe4W3BjYIrw3p7Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\material3-lite.css", "FileLength": 456111, "LastWriteTime": "2025-07-02T05:37:29.4526866+00:00"}, "QT3yZjBTV/2BkIdc3pmVFZrhkElj5xRvRGJ8/yyUmhA=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\h5th63hdh4-g01jva92hl.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "tailwind.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\tailwind.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vvh8u0r53t", "Integrity": "i9r7E+G7H1I0XmlH71T+uhgH0c2PTp4X/WtQ4wYTCTg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\tailwind.css", "FileLength": 438616, "LastWriteTime": "2025-07-02T05:37:29.7167247+00:00"}, "AGUapJjO1ZJUxNQEwOBOXHFF2v41fyFN1axataRSfyc=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\sxqxspocya-yyhspbp1o2.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "tailwind-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\tailwind-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "eybj5slclb", "Integrity": "RihgOBzf4qQ1Xiy6W5f5IoYWd64EQyIhY30W6V1q//k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\tailwind-dark.css", "FileLength": 441602, "LastWriteTime": "2025-07-02T05:37:28.1107146+00:00"}, "xr01euIXr6ZDbWeQCYXXAgdcs+mCkFX4hvwXt4yS+Nk=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\rd6io8kvbb-mwvd6pk8i0.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "tailwind-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\tailwind-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3p5hyv5uba", "Integrity": "F0XHK/T6Azl1qsKpbAekWhxYryjFwGgKnonzyj+O3ec=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\tailwind-dark-lite.css", "FileLength": 441610, "LastWriteTime": "2025-07-02T05:37:28.3701721+00:00"}, "BnRNu8iFtsPgdREGqIojQgjeJE8nt0IaUBaQuF/7t1c=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\g3fd5cikf4-2hecvjsdbn.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "tailwind-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\tailwind-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dw49makaib", "Integrity": "DX/OxBlIuUQzGT426ip+i4s3JdPCRnXlh72ckpPqbak=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\tailwind-lite.css", "FileLength": 438619, "LastWriteTime": "2025-07-02T05:37:28.6253046+00:00"}, "gKmUaTJPzQ+/hCz8Hxi04dydwhYjVDtcuHRxtSKOQXQ=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\k08v6vvvwj-hzjg487txq.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "tailwind3.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\tailwind3.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5rhgdmkumq", "Integrity": "4cLZamXUivZmhnFx5i8X9vs194Ht+qCOHT+eE/7Ldsc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\tailwind3.css", "FileLength": 498999, "LastWriteTime": "2025-07-02T05:37:28.9301951+00:00"}, "LQFT/JeXdkFJwFzub70gBl0jINmNDCfY56COztgnn70=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\npcbabgmoa-5m8vl9oe2v.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "tailwind3-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\tailwind3-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ynh6ly6s5w", "Integrity": "EgR5BU2R4OLRGbyxNc3wqHYthuTpoE4OtTV224WPjjk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\tailwind3-dark.css", "FileLength": 496817, "LastWriteTime": "2025-07-02T05:37:29.2386063+00:00"}, "jDLHQ/KW/O7b/NNXTJrSVBahjA/pXRKSApn2n2R4GNE=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\4cc4cl8qb1-vh74rx7xtg.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "tailwind3-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\tailwind3-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xmgynvhkaz", "Integrity": "RxTNSGwVGvnbzFd3V7NhdOz63dfRwxdKDLjo9IoAUW8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\tailwind3-dark-lite.css", "FileLength": 496820, "LastWriteTime": "2025-07-02T05:37:29.5668032+00:00"}, "cTXgYH4zF/F+7KL5uctY6QMN97IOBjQlOcBUCNpSIeQ=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\3itjbw64bu-mjvqjfbph1.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "tailwind3-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\tailwind3-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rxyj1sox8g", "Integrity": "YXem+cBIeeOi9IGVVRLRrNyo5rsjYED0NSTKfjCBB1A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.38\\staticwebassets\\tailwind3-lite.css", "FileLength": 499003, "LastWriteTime": "2025-07-02T05:37:29.8398646+00:00"}, "YBznBpoglnBhafKSfzSBgSijOmYrsRwgjdzV3CAirKc=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\2tby22c7r1-lbmfub4x2c.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/data.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\data.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5lq09xg615", "Integrity": "ZyxeajUPPZneNfnc/64LZ5bvt0fZIEBvlqHK4weTcJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\data.min.js", "FileLength": 24740, "LastWriteTime": "2025-07-02T05:37:29.8518321+00:00"}, "h+jQV8d9SNWS1YQBzC4bR3WsIyHFD65fvlkU5DPgb0w=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\ddg9dsjctj-tz7qeeb3lo.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/navigationsbase.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\navigationsbase.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nlic41vr1t", "Integrity": "1T49X7JJ3cfxl5b7mBQnowF3MftDE9jIIyO2CLAp0kw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\navigationsbase.min.js", "FileLength": 3974, "LastWriteTime": "2025-07-02T05:37:27.9016379+00:00"}, "kuT0svERoeAoGD8fzXgQ1bCf8IkwGYY9p4mRERKFfKg=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\s0a6p2lsn4-e21v6zo5rh.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/popup.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\popup.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ecwtysjdzk", "Integrity": "AvVOOZ4gmkSiJ706FQsl9A+Y8B2L/WIc4O+Yi1Zg5UU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\popup.min.js", "FileLength": 4244, "LastWriteTime": "2025-07-02T05:37:27.9051588+00:00"}, "5lfxr6o2ZxtZdJzgCQD8dnbZtSLrm8+LsIz8GDBrA5s=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\bmz5nvmby1-io6jyjpz2x.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/popupsbase.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\popupsbase.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ngpchzx5hx", "Integrity": "53jOHc5D6fZHple8iQHr/Bnj1HANBz45CMiV5Nt1Ork=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\popupsbase.min.js", "FileLength": 4426, "LastWriteTime": "2025-07-02T05:37:27.9125464+00:00"}, "HQtf0KX7N6bd+j01XMWSrBtU7swCoXVmgR5Kwmf5III=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\ut6zzcs2sj-h37rwa6sef.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-accordion.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-accordion.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y6afn7c2xa", "Integrity": "puuiu9UH8eSgkaV9wXS6f3a/XShQP+rWk6fZGfPwRcc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-accordion.min.js", "FileLength": 3455, "LastWriteTime": "2025-07-02T05:37:27.9155654+00:00"}, "WPjbrrgBz2rIiEPMzrUZEb9VCN6kbczWy164EwdCFYs=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\xhreyxaaqf-smz9altsvc.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-accumulation-chart.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-accumulation-chart.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dr4<PERSON><PERSON><PERSON><PERSON><PERSON>", "Integrity": "J+fy287a9HKPkmLQo8GxFT5oqnjM/YpT6mHcClY3b6Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-accumulation-chart.min.js", "FileLength": 6148, "LastWriteTime": "2025-07-02T05:37:27.923195+00:00"}, "XF8qvQaCMkmowm7YhVp6nrqxCO6vm0u/jCJMV6EPD+8=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\d405vrip82-unrs18j4q4.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-ai-assistview.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-ai-assistview.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ic8dimlr7z", "Integrity": "fhrfu4Gj6nG9+/8NMc5QE4IwuBEGHpluvIPn9Yfb2I0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-ai-assistview.min.js", "FileLength": 3460, "LastWriteTime": "2025-07-02T05:37:25.3095436+00:00"}, "0mj3HvYX5HFUftQeFasF7IecZLmwjFuRoI2qMQiCIgc=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\in9q5p7h0u-uhjtko12nt.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-barcode.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-barcode.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f9brpxfno2", "Integrity": "w/5cwZoslfk+G7j1sU9xwkpar9vq9AHastamISW8ADY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-barcode.min.js", "FileLength": 2546, "LastWriteTime": "2025-07-02T05:37:25.3157823+00:00"}, "oYsmOGaVkLHkxZYkayM8azrBrKYc5PJqMkrxvE2XpN0=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\0f3xp4yca7-moh18msdqv.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-breadcrumb.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-breadcrumb.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7gfypvrhew", "Integrity": "twdKmNiPfn0oC9auQo0RYKxs0ekLIO5f2fv0QQUOBS0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-breadcrumb.min.js", "FileLength": 1787, "LastWriteTime": "2025-07-02T05:37:25.3204775+00:00"}, "HXYoa9dW1ugA7gkr5C4F8zi1T9bEScBhKwIX1d4/qg0=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\9oodxfd7jy-dhwsiub057.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-bullet-chart.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-bullet-chart.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f7qstrww33", "Integrity": "q/zDH3nUvLUdg3Yq6bBTn7LVCw/yLJmLzgGpwV3O8zw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-bullet-chart.min.js", "FileLength": 2440, "LastWriteTime": "2025-07-02T05:37:25.3251919+00:00"}, "+siPgZ2x9luWolpmIJdLUyLyYUuo+Uqi3fecuhsCgAM=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\iyvfbpj4yj-4177zm8goj.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-calendar.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-calendar.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "im8is1z8lj", "Integrity": "KmznJV0FOfUEFlluDlsrWY6p6ILQO36HJd7jJSIOikU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-calendar.min.js", "FileLength": 1014, "LastWriteTime": "2025-07-02T05:37:25.3312353+00:00"}, "39giKNKS6gTMC30uAbXuYSy7/ZOePHW29sVPjK1mK3o=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\44mfw27k7i-zscpaq4nct.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-carousel.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-carousel.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "irdu8aaklx", "Integrity": "LrHJvmy2lktiCD1AIMM7/EQtR7Q0BEuMWLltM4KvUnM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-carousel.min.js", "FileLength": 2140, "LastWriteTime": "2025-07-02T05:37:25.3392972+00:00"}, "NSczrieEI7Lyu+PaUjBTfcU0rP1lAg0tG+1fmdhSdfc=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\cfvyxuh1qt-alvxtlf4hk.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-chart.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-chart.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4ragemyxhp", "Integrity": "cOC3ORPXaJ8p67+RbjJ3adlUnZ1kBjSzFrL0yex1HOQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-chart.min.js", "FileLength": 54271, "LastWriteTime": "2025-07-02T05:37:25.394174+00:00"}, "YjDZQzWPrWE49nkK+XXQygHda6ug8p9rh/fA38Tewe0=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\wye317dvcg-jkoxdnufqr.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-chart3D.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-chart3D.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gfeww053q6", "Integrity": "9L6s2fg6mw64dWi124ZeGQrFM1NdgtaQLZRhg6wB/qs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-chart3D.min.js", "FileLength": 15883, "LastWriteTime": "2025-07-02T05:37:25.4080189+00:00"}, "Y7EIcpWyUCvLizt9+f6qTiutGRGVUg6IgPpTGmHxLuA=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\1ekojsgjxz-sm7pomxcp2.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-chat-ui.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-chat-ui.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "11gccks2s4", "Integrity": "JXjvVVPHSYk8I+g1V8QoutZ3M5P2u43pYEoruC1Y8Hc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-chat-ui.min.js", "FileLength": 3725, "LastWriteTime": "2025-07-02T05:37:25.4140634+00:00"}, "OCQ7dv470gNVtLTgtKjwvb2p9MjHJwa9xtmD84Mn3DA=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\y6tbkm7tzg-xzerttii31.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-circulargauge.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-circulargauge.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o0rcd5bour", "Integrity": "ONuuDB9gziOlhC+PpC4MIslzjxMiN3nEZQ9JlF60yvI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-circulargauge.min.js", "FileLength": 6777, "LastWriteTime": "2025-07-02T05:37:25.4316781+00:00"}, "HjOlaAosmA8liU4YZ6BxpVbbnCrxSVH12/g/+MEu/go=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\elmu9o8lon-y2mb336vwc.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-colorpicker.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-colorpicker.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3kq7ft0t5z", "Integrity": "M8W4WiEG2wt8Kma53HpaUD1w+47GFsRAGPXeMaV/qV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-colorpicker.min.js", "FileLength": 2153, "LastWriteTime": "2025-07-02T05:37:25.4357092+00:00"}, "Kxk1iBMO7COuTgQySAhbE8/QJjyvkp/35ly1ln+4EdY=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\rwu2eraxoe-gpm7kf0s08.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-contextmenu.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-contextmenu.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1ysdrg5g1d", "Integrity": "yV6M4Hvr9dZhR01zqRatasNbR5AZkFQp+o7OpAiNbZM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-contextmenu.min.js", "FileLength": 4905, "LastWriteTime": "2025-07-02T05:37:25.4486784+00:00"}, "MUsov/AoWaJMH8bOj8wQlyM6hAhhtJkJRxE+x8y6q8M=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\12tizwxlsz-g4634wyurm.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-dashboard-layout.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-dashboard-layout.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gnjrvkt3co", "Integrity": "PHt+du1otGFIIyzFldtUESnxJpF5ydm8IJ+kX5vXwGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-dashboard-layout.min.js", "FileLength": 10423, "LastWriteTime": "2025-07-02T05:37:25.4567315+00:00"}, "ENKseWtjnCZ2KRLRayqOBnjr68cFDhMpOqflwoXCe4w=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\koe3iulnl6-aj7f5d3zjz.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-datepicker.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-datepicker.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oq12ib9qox", "Integrity": "sByWO58uc1rqUo/s4TDpXJ8jhGLgJYNiI35mOqvQDn0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-datepicker.min.js", "FileLength": 8853, "LastWriteTime": "2025-07-02T05:37:25.4708153+00:00"}, "Yu1Q6DQzhlg7ZvSVGVRxiE9a+OMYAqTBkom0ENX5hYI=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\cpm9m8wx2t-at55n2zic8.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-daterangepicker.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-daterangepicker.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lzkf11ucke", "Integrity": "AT3KZzEwlS1xmxGY8Wq4NG2cfd959F+ajffBOeRSpcI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-daterangepicker.min.js", "FileLength": 4034, "LastWriteTime": "2025-07-02T05:37:25.4748369+00:00"}, "TWUbOM2b3zUQIGxnn6WaLrNWtO4bBLGl2e0zRlOW2uE=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\d4g29a2e03-fbpyz5cl4t.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-diagramcomponent.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-diagramcomponent.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0ydgp7yexg", "Integrity": "mH/fWlb8jnw6ckc3E/B91cMDETfNBmN1hQP9mS36Bjk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-diagramcomponent.min.js", "FileLength": 16479, "LastWriteTime": "2025-07-02T05:37:25.4866546+00:00"}, "mfnu8N7GJoW0ALlFcqPJS+29c3YZ9bNqFMvcmX2iFCA=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\xc1o58ypca-jvixe1avtv.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-dialog.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-dialog.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tx13k4tfts", "Integrity": "keAEBtJ4Ct0To931Y2srrAL40jyX2cuwIQ5Ji1P+0AQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-dialog.min.js", "FileLength": 5961, "LastWriteTime": "2025-07-02T05:37:25.4926882+00:00"}, "lKDjZrNEptVFqVPHN7hWFugOEMMHw6TLwmPZe5Q/PSs=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\e2fdz9um82-mhlalgzebk.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-drop-down-button.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-drop-down-button.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u7u4rb8xcb", "Integrity": "BscslgRDphT1cT2YjyEp7K/kFO0nefglbtXLAVitYqA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-drop-down-button.min.js", "FileLength": 2727, "LastWriteTime": "2025-07-02T05:37:25.4964527+00:00"}, "+mJk9dUpYdum7DhQ1SJJMF0xO+gY5CfH1kqhRW+pNCo=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\3s9nhql60o-j9jqs4yjwm.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-dropdownlist.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-dropdownlist.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rybn5rotqe", "Integrity": "rTLnzymGyB2yGj4O1G+k0A5fw24h/hZsZHX2OeOYG98=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-dropdownlist.min.js", "FileLength": 9837, "LastWriteTime": "2025-07-02T05:37:25.5065243+00:00"}, "XbpKyi+6MGkEu+XPShsMI2CFUKjvKVGERL+3j0kTBVE=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\ebbn4z5zip-4h1z5eanmv.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-dropdowntree.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-dropdowntree.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rzt30b357r", "Integrity": "lzb/Rlab3Hwc/w44q+rEoIsiUCAVdcUPPqXiPzZv/vk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-dropdowntree.min.js", "FileLength": 5963, "LastWriteTime": "2025-07-02T05:37:25.5143189+00:00"}, "f00S106Y82V+RXzsL6lSV0hITvZD6DnPSdgkxYUpPbk=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\w2cakg9ukp-d0n5qqyh5a.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-filemanager.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-filemanager.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9w6n24puq3", "Integrity": "V5vns6UXGtjChNf63LzjXVQOW3fR1Jy/+r7fA+hl5kk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-filemanager.min.js", "FileLength": 7036, "LastWriteTime": "2025-07-02T05:37:25.5183399+00:00"}, "Hw9M8GdkHQvaQR0zd492d197nC0cC/OjNBRDgzrUhZw=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\uehbyupll5-jqswcymb5t.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-floating-action-button.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-floating-action-button.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ae81xqxzu9", "Integrity": "PuYGBOXS7zCaPUEOeywlv7DD19jPY7u6vv4ub0KxLeY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-floating-action-button.min.js", "FileLength": 961, "LastWriteTime": "2025-07-02T05:37:25.5203559+00:00"}, "KAaWi8GvjLL4SIIfzDg5OjTK2uN2/ALfT6grcwtH/zY=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\6ytg84iue8-a2g2sajptc.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-gantt.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-gantt.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1652p9yox0", "Integrity": "V9xWPpKliOndyXR1R8fl9XxfiW1INTXPkv3RfO+RSVQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-gantt.min.js", "FileLength": 15059, "LastWriteTime": "2025-07-02T05:37:25.5301636+00:00"}, "vzoIs+JFr72YSgjtQsrHKs6Hd4WTmmy6xcko9QQ+beI=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\fidxlt0g3v-n8n5vmqlnc.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-grid.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-grid.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "46k4x9587h", "Integrity": "HBS5uXWpYrYu2n+dFCSwsdsks6iuEr1KD9hp3Ln6fWw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-grid.min.js", "FileLength": 63677, "LastWriteTime": "2025-07-02T05:37:25.571877+00:00"}, "BoUFkU9ixhm7q+AH2/5i6emJdOuFQbrUgXQBelT/9lQ=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\82nczmsris-onzorqk3vw.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-heatmap.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-heatmap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0ylwaynclw", "Integrity": "6EbUJUjXOXf0dCR1XIdeGyB5snRuXqdWN6g0YhpMk3M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-heatmap.min.js", "FileLength": 5391, "LastWriteTime": "2025-07-02T05:37:25.5776671+00:00"}, "wjihaoZUXsLrIGO3QjnnvT1p6VIST27yYvhfoIrYw7E=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\jjmy76blcj-wcetg2bjwm.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-image-editor.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-image-editor.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t74uyykb3w", "Integrity": "2ZoTDZHBrM0GAok64X0fpZPBuOL5jU+AG6ipKvJx4GQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-image-editor.min.js", "FileLength": 154820, "LastWriteTime": "2025-07-02T05:37:25.6879527+00:00"}, "IjsyiAjh9kE2Il5+cTTqe8COSFbDoQH3zmFJWhSDzqU=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\vlup8812vg-d7wk9jvbd7.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-inplaceeditor.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-inplaceeditor.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cedh5xnmfs", "Integrity": "7Gih1x722qLJytmhH3jD21smdzT3qWcKES8Sk2EQvmc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-inplaceeditor.min.js", "FileLength": 2199, "LastWriteTime": "2025-07-02T05:37:25.6959983+00:00"}, "ln0wf84l7x+ef2gixlqpBX+YjYG5Bc2Pj0zOmzxetwA=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\vjes1wfbht-9z840kepjs.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-kanban.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-kanban.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8s19pfsvk6", "Integrity": "uLnNVhwcHtOzbk2+4WI0q5r39LYMiBps73J+kfK6hZY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-kanban.min.js", "FileLength": 9539, "LastWriteTime": "2025-07-02T05:37:25.7138444+00:00"}, "liijwWnRyvS8xF7qCuA2nae4lDXOfa1GbKPJ8mPg758=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\cjlbdrwbmy-scjsgixwfg.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-lineargauge.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-lineargauge.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b4ac1aka1a", "Integrity": "xXgOLMxBUA+UIgVMfBdT69x0VAphE4i6L30AOWu/Y2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-lineargauge.min.js", "FileLength": 5171, "LastWriteTime": "2025-07-02T05:37:25.3126705+00:00"}, "DqMoFG5ynMrf2A/3C/5vD1zG9rOVvSN6uHhA7jMwjnw=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\hpxslzcfcx-re75mv4fbg.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-listbox.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-listbox.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "05pcrqfljf", "Integrity": "vCIyD+JkGEmPmXDRnwwFpVZHzS606+AmDiynfNNG7RU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-listbox.min.js", "FileLength": 2114, "LastWriteTime": "2025-07-02T05:37:25.3173393+00:00"}, "Wse2beg0SVp8plzowDjm9KwZQyW7XwDAfyPbe6RA5Es=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\czt8ptpkp9-l1qg22ptge.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-listview.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-listview.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d9i9fu0q2l", "Integrity": "TxSB7iVhv8q3rjICG15uh1KOtKhXrAtehSr8sBsTdn4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-listview.min.js", "FileLength": 5397, "LastWriteTime": "2025-07-02T05:37:25.3272051+00:00"}, "ONxUUsIePeUAzLUpm8q/Gp3BroHyXh9FHtzvxebtczg=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\7s51rvoei9-4sg0vzgfr6.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-maps.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-maps.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jd46euzm7q", "Integrity": "pxESTctyLJpiJA5fNL5d1/UmoF90oYRc/K64/2e+TAw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-maps.min.js", "FileLength": 29078, "LastWriteTime": "2025-07-02T05:37:25.3566824+00:00"}, "FoHZyPRzXOEaqPe3cyejHxEFrDu3Ydy6xX1qndb6j2Y=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\iukitgqwf9-41qhkuzrqf.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-maskedtextbox.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-maskedtextbox.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iuf35hooxp", "Integrity": "+vtLiXTYXMRr6DL7Uq2Wa7LLnFsplwHqX9uf7AVEnCs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-maskedtextbox.min.js", "FileLength": 2775, "LastWriteTime": "2025-07-02T05:37:25.3647301+00:00"}, "mDTLbXiHHsCIunY00/3GXdseOj0Qu6hxqQuD5zzMNzI=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\be1fr8o4r3-ell0dovghz.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-mention.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-mention.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ehljb17d1a", "Integrity": "fs3R3/9JdOi0/P5EQcUWqIMDcj9Wx34a05cmhoU1NAk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-mention.min.js", "FileLength": 5985, "LastWriteTime": "2025-07-02T05:37:25.3705089+00:00"}, "GvlsNpdsxGrpO66/ZTY+vS2TiSuDyGCrmCXf3uYva5I=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\gtnoncceba-n1ixjsnvp0.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-menu.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-menu.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "x7b4365vru", "Integrity": "zdzHBXEh4rUnZtK0trxze+IIdNXDCQVauUsXgOS4rmA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-menu.min.js", "FileLength": 4105, "LastWriteTime": "2025-07-02T05:37:25.3760557+00:00"}, "v4Bb1+MB0SuamzaLFxokbWBFJ2kYKig+5Syu9E5H+9g=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\lkespo2mpc-toyz4r717u.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-multicolumncombobox.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-multicolumncombobox.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p25xsdtm9m", "Integrity": "8dce9YYIoY0vOpLifnG4cFEShYrNS1gnaI8q9mfqdTE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-multicolumncombobox.min.js", "FileLength": 2340, "LastWriteTime": "2025-07-02T05:37:25.3820963+00:00"}, "Yt6vdnpiZpJwvb59ZsJLjhyInZrslV4X+UEvVIlzdyI=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\xzqibnkirt-r9lhpakalf.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-multiselect.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-multiselect.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "311u2lbfwl", "Integrity": "t4Z33y4zaltSBnp+uT3Ccf1vlngH+eVhteDk4q3geh4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-multiselect.min.js", "FileLength": 8692, "LastWriteTime": "2025-07-02T05:37:25.3921625+00:00"}, "kXo4/BWhspnlzfA2tZw4YiQZsFAWS8NPIgT1KYhIYLM=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\itlbf61hm6-89kppbox7d.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-numerictextbox.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-numerictextbox.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lxxrg888kh", "Integrity": "d2t6wXvc9jHHW1DCkWpGLoxxJUvbSy0Mr00lt9zM3S8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-numerictextbox.min.js", "FileLength": 3255, "LastWriteTime": "2025-07-02T05:37:25.3982004+00:00"}, "yaBN6FjqN6KDl7LLloVx3w2mPvmwt9sMONWQvJqrOBg=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\s64mkyknem-haaveb51nn.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-otp-input.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-otp-input.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u5dnax11cg", "Integrity": "YmBZdfYfdAgPxlx9i99uAPNYLCFgvIr+76K9glJGQfs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-otp-input.min.js", "FileLength": 1156, "LastWriteTime": "2025-07-02T05:37:25.399961+00:00"}, "xl2N1XzBqIz/ByFZshROtA12+WJTL4CkBoh40tVr8B0=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\uofcb8dzuy-f60q30p0f6.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-pager.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-pager.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lx3qmx8o0j", "Integrity": "l++/UHCXoNWN/Zr3UdCCyKwodnBASRski2mLyqD0Lho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-pager.min.js", "FileLength": 2604, "LastWriteTime": "2025-07-02T05:37:25.4100352+00:00"}, "9mZdzMJpjT7fGS/iE+MvWDIyaSgNB7sJzBQp2Iv3x8A=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\dkkcf86snh-8262d5tdv0.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-pivotview.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-pivotview.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sldvdrav9a", "Integrity": "oC9kwdlwmEJPH01V2XvSEMRxLZGgr73itMEJGbc+9sU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-pivotview.min.js", "FileLength": 20147, "LastWriteTime": "2025-07-02T05:37:25.4357092+00:00"}, "aiFcU8N4Q0yo+o586DWXK+JqhBCfHqjaMHv4fCMFQ+0=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\apf3r30luq-tl2nx7mnd1.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-progressbar.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-progressbar.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6ngcs9hmnl", "Integrity": "vjCFpUbebHfjHPvQBRCEl+lPUorJJp1chp7vzeHuiiA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-progressbar.min.js", "FileLength": 3844, "LastWriteTime": "2025-07-02T05:37:25.4417518+00:00"}, "80NxCiFlALn6GpHOO4Kh4sCLE4+HxxX2H29VBbZ4z9Q=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\9h9zfn7yek-87vsa66xg6.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-querybuilder.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-querybuilder.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ll3zd2rrtq", "Integrity": "+eEHn0wYB3Jyn8h8RtP9JpV0t858+1TM19BOpf+g6Jg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-querybuilder.min.js", "FileLength": 2043, "LastWriteTime": "2025-07-02T05:37:25.4457766+00:00"}, "w9KSshSzPTyS4rw9cZ0cfUGdeobCEUDsCPVjRferlR4=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\jcgtm963n0-7u0ofejoqe.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-range-navigator.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-range-navigator.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "36yf7m2jft", "Integrity": "RUbnHUHmAbXH8X0yF8ul194zwfGaVHMg2aJsH5p9w5w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-range-navigator.min.js", "FileLength": 4400, "LastWriteTime": "2025-07-02T05:37:25.4506921+00:00"}, "c1iVofB5cstgqzAnSsxL6zNVl/JDPQQVvbg/Ttj4pjI=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\6xby58i1ne-7xa2kvfb9p.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-rating.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-rating.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i7dihks3du", "Integrity": "XhRLq+YhwQhqpJxPHYkGIQqyxtU378nQnB5B4yv2I/s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-rating.min.js", "FileLength": 2494, "LastWriteTime": "2025-07-02T05:37:25.4547191+00:00"}, "rGLbwNrNfZ9N8GCt+5mV+htMW4NSJ2uyeXEfI1s0Rhg=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\5qjtvvhsgz-nwuph03q6o.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-ribbon.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-ribbon.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fvvl1s7yuj", "Integrity": "ZerUaVJz4nuZbFJinZCaWPGCGNTq4DXQ8DcqQahQCH4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-ribbon.min.js", "FileLength": 15744, "LastWriteTime": "2025-07-02T05:37:25.4708153+00:00"}, "zR0nSpZj+7q9j5w+8weg8LAyet+YDSmB7bQBxEAjDmU=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\16wg0iqttf-qdo3iam7qo.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-richtexteditor.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-richtexteditor.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ddy2o4tcfp", "Integrity": "f22pj+Sp/lHQ3r5jvtDYmghbIrCRrQ2xsGDC5dt0+7I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-richtexteditor.min.js", "FileLength": 197587, "LastWriteTime": "2025-07-02T05:37:25.6094866+00:00"}, "raMKAQOlPdxlOF91cH+JDAnzM0u7Vv8wVT9BUkIVJPE=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\qxbdc57p9r-ryl1wwe4zu.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-sankey.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-sankey.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nvh9s8pbcq", "Integrity": "w84CST5gcLMixtLWct7/m876qUz0eakbkfs7OdmBYIg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-sankey.min.js", "FileLength": 3329, "LastWriteTime": "2025-07-02T05:37:25.6114973+00:00"}, "6obNe9eJ+3VUHUOGzq4MFk6lT1J+d/y5EjnHwCKeErU=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\3a4nupymxx-2suvi71668.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-schedule.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-schedule.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5yj1w5ghgp", "Integrity": "w1A+2AuQAMil9PU4uWzQdKYd6H8DUSz0rJQNyUWv6Fk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-schedule.min.js", "FileLength": 51250, "LastWriteTime": "2025-07-02T05:37:25.643153+00:00"}, "jBuGNOd4IIYhI2IBguwURQxTVNa8RPObglAhzfSwgFo=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\42ck71d2wv-fyw7d8m088.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-sidebar.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-sidebar.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xdn5x40ug8", "Integrity": "pdZnpIaTB99wcumaKtHuCKEN11d0jgTPDEtqFRHGeVU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-sidebar.min.js", "FileLength": 2960, "LastWriteTime": "2025-07-02T05:37:25.6491865+00:00"}, "sExR015HdrJIHCF91atJp8oQhZTL3VlnMOOMsuxp3yk=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\hruzog0nao-hhnlivrvks.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-signature.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-signature.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "blx6iaao6l", "Integrity": "cbaMlUmz+inAJJsxN33oknl2WRptoQ+WV0M5sGZqRSI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-signature.min.js", "FileLength": 5961, "LastWriteTime": "2025-07-02T05:37:25.6529506+00:00"}, "knGPp6lvB24+aUWIJ9KMgFDE9bx/Aso/HaYUk6Tc2pw=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\8z7q0tcoh7-og11zpg0ew.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-slider.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-slider.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "74okaj31ua", "Integrity": "6WI1i4BATQiXBx+7blnjsnWWgEtWLGHUiEq5HgUR9v8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-slider.min.js", "FileLength": 6450, "LastWriteTime": "2025-07-02T05:37:25.6569708+00:00"}, "XkaClwI3euGmg5c16F9hsC/nDy0MFnlar4UOwMo3g4Q=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\r4s30r6yiw-qolx4a46gj.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-smith-chart.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-smith-chart.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m3ha8atvyx", "Integrity": "7op8DBpzgOnS1rJDcPqNYJp2+youPqJWedUz814vVHs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-smith-chart.min.js", "FileLength": 4035, "LastWriteTime": "2025-07-02T05:37:25.6609989+00:00"}, "//xzVrcRMv+IUAabv4NxFB1mBLwFfdcrjLZC+BH1I30=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\6jncl9y7mi-t65am6tzqp.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-sparkline.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-sparkline.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qn53rdfc5f", "Integrity": "moGBKyYkHdP9clejKzporH+HaIiOcJBNApnzlMKIVpg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-sparkline.min.js", "FileLength": 2405, "LastWriteTime": "2025-07-02T05:37:25.6670357+00:00"}, "iB5BXM4Adu+Fc/2onGyXF5hksjXTFNYpd9TcJUzwZh4=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\lsawkhetsb-qnuuwjt1u6.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-speechtotext.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-speechtotext.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nw59a3gl7i", "Integrity": "J/wxrAu3bhgpvgM+4TfYKhNDQKLqB5cJE0qNo8sJ7Jw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-speechtotext.min.js", "FileLength": 1112, "LastWriteTime": "2025-07-02T05:37:25.6688041+00:00"}, "zOi4rKE2T7e9OiVXu2qwSDcRyvcI1RojS/MY0biTHWk=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\3ex2m3if29-s1ivu0bk89.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-speeddial.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-speeddial.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fz57za0qxp", "Integrity": "CwV4uof/rLtX41WMIt3TL+Hi3hoOrDfyep+WqRwmMes=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-speeddial.min.js", "FileLength": 2741, "LastWriteTime": "2025-07-02T05:37:25.6788618+00:00"}, "eFhIoaBxvA2oYYOW8MRAc7D0GxCOgqXMKC0DseNp3L0=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\0rac66dpmt-3q2mhic92f.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-spinner.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-spinner.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h70li32mp5", "Integrity": "TdLwnOj5+2o1G20nfJvNt7LIc6MPWSKnkszqC4iftho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-spinner.min.js", "FileLength": 670, "LastWriteTime": "2025-07-02T05:37:25.3111084+00:00"}, "Ac/W1MU7iG1n2vQRVD75ieA/WT8f3w9EKFLFKx1wPzM=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\aadd9iinhj-p08bvg59ln.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-splitter.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-splitter.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f4n9q1bsva", "Integrity": "T4tnAmmb8BFUM/7ywMuORidzKRy+xLbxdtA1lgreBRU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-splitter.min.js", "FileLength": 9478, "LastWriteTime": "2025-07-02T05:37:25.3236294+00:00"}, "wiPUMeKW95OYA0z5NkE8UsiKNxl03KRBJ284bH80Y4Y=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\nxqjd904l0-07idhmp8z0.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-stepper.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-stepper.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z35zn179qn", "Integrity": "YTCv5d6WEpzvmqfJ5zWughuJ1wBx+9t+nWONaYA+pio=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-stepper.min.js", "FileLength": 3579, "LastWriteTime": "2025-07-02T05:37:25.32922+00:00"}, "IdZK7NxXjrUCbSpYhELhHqd6p4gyIQVHjoU3SCYRfeg=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\5esrip7fz2-4gnrktba0w.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-stock-chart.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-stock-chart.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "14cvisjkws", "Integrity": "7z6Xg10tpkmE2/E4HqzhME9sdTI40J1nv0LRbsQgVIM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-stock-chart.min.js", "FileLength": 3662, "LastWriteTime": "2025-07-02T05:37:25.3332514+00:00"}, "WCdIE1lZCkQupHlLSqiwoLF7XeYEJM5v0wlDtPLWVmM=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\zr19gib3w2-aa4y9xtfb8.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-svg-export.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-svg-export.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "15r8q83608", "Integrity": "ZWXYTODSKeAL04mT13uBwWa7+TSoIkQ3JvW12C6DLQ4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-svg-export.min.js", "FileLength": 3884, "LastWriteTime": "2025-07-02T05:37:25.3506452+00:00"}, "Zmf22dR1/sfySG+8zz+4nw1XtQFleANGaVEu+nezo3Y=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\b3svqv6skf-2bp4mwyfyc.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-tab.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-tab.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iz8wpsx0f8", "Integrity": "fq7RUfmwIT0lyzkK9E5J8FKtDn5sK8tKAhhZGBjX9bY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-tab.min.js", "FileLength": 7866, "LastWriteTime": "2025-07-02T05:37:25.3607066+00:00"}, "P9QSmYrFSl2tAanwhoDtKKXHmZKDDqgxLuFEFY1M/3E=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\t9ldm31sr4-c52q2j7z6m.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-textarea.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-textarea.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qdqiayvh9e", "Integrity": "DpjN/+iCqe59U840Ok+Ngb6VFGg7zit0SLgkzMo4ZJo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-textarea.min.js", "FileLength": 810, "LastWriteTime": "2025-07-02T05:37:25.3627202+00:00"}, "v5HxpD/jYnj4/PfdMSX4v53EGRFb92+yHfD2WhHXZUc=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\5sm0ch5rs2-u9jvp43nw3.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-textbox.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-textbox.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9s6e601jt2", "Integrity": "J1Bp440IQOHJZM8jFE4ik8FSuIKnoIlm5S/9efs1Y1M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-textbox.min.js", "FileLength": 1298, "LastWriteTime": "2025-07-02T05:37:25.3667431+00:00"}, "qmc3lU5cMy9Y+nb66g4soP/TtcEH2UfG9SwqO6yr+Cs=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\8u9jrxeb4z-z6601xyl13.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-timepicker.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-timepicker.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "edoav0te6j", "Integrity": "HpffH/te0xGkRTtDbYnVnF+S+i49LBs07liVsComND0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-timepicker.min.js", "FileLength": 7657, "LastWriteTime": "2025-07-02T05:37:25.3780645+00:00"}, "AeL9EfhXT431k2+C4aCes0WzeqkOgPA33UTVSzotlcg=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\qr1eqiq81d-zfzi686el9.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-toast.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-toast.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "euu9wpggyh", "Integrity": "mtc/nV9YaAGjfWGYqT8j29u6lKRpYnRzoJUa/Q/94Gs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-toast.min.js", "FileLength": 2428, "LastWriteTime": "2025-07-02T05:37:25.3820963+00:00"}, "Za4OmlwQVGR1bl3lPw26Vii6XJXUpKQPN/RK+gndw3w=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\qeg02v8nug-ru01t6gedm.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-toolbar.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-toolbar.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "44bel5bqwq", "Integrity": "xMip6wNEmXde63tmi79SoFwB98aVsYFPi6JXgkz/IhA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-toolbar.min.js", "FileLength": 9553, "LastWriteTime": "2025-07-02T05:37:25.3901473+00:00"}, "c8ZMDyWOvCmlWqSquXSkLueBGAnIKo/k5GnXBYbPwwI=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\6iv2gqi52w-18yc1apabw.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-tooltip.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-tooltip.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "echdbgz5fs", "Integrity": "8ssc9dZeN/+shYIgHCe58P9CiNSZC6+FJKlBGguqcwE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-tooltip.min.js", "FileLength": 7491, "LastWriteTime": "2025-07-02T05:37:25.399961+00:00"}, "M2/0gwR4YHH68X48NrVBJR9qzdsLIkCR7xnrDfuJfzg=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\d8h16lm625-47w09bl5c0.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-treegrid.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-treegrid.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l553zxidwo", "Integrity": "JzzFUS+wwZyZQmDI/JiIlh5GXFWX2EFHaZ2HlZNFVyM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-treegrid.min.js", "FileLength": 7660, "LastWriteTime": "2025-07-02T05:37:25.4140634+00:00"}, "xqjVKkJdbu88C91LyPqimVlyt7bKT9ZBeywBjQC/nKc=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\tuovec8evf-snkiu0ogdt.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-treemap.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-treemap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u7md6hxcjt", "Integrity": "umvbBv9B6dNliymjO95U6tQiiWxg6hf+WEMrw6yYLHI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-treemap.min.js", "FileLength": 2918, "LastWriteTime": "2025-07-02T05:37:25.4218692+00:00"}, "nP+Ah8mlISpMbbTU3xRvwveq7zElQHpK/2x52+GgTwY=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\1ad0wz8wh3-mkkha08vq2.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-treeview.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-treeview.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t717wvfbfr", "Integrity": "F1JDymApgq/+Q1elku88oyYQ3CngdX9ZjTSrarNMLKc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-treeview.min.js", "FileLength": 12154, "LastWriteTime": "2025-07-02T05:37:25.4377247+00:00"}, "kH9hYautY3RzEPSfiUmZfMmg41BNSUm/lq1007hFbY0=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\j3374ns99t-17wis7ysky.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-uploader.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-uploader.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8futobcfyt", "Integrity": "vBw+7oBvARXIann1Yo4LMZiGcFclpvs6mSDitahHNDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sf-uploader.min.js", "FileLength": 19249, "LastWriteTime": "2025-07-02T05:37:25.4607528+00:00"}, "EPyweL3usrHyGxNyJxIGd5Be+T7XNC+ZzvHhpVGi5Xg=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\ybfh3feh32-gz6e3atemk.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sortable.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sortable.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "imgsbjhlkh", "Integrity": "1lV0KfopPnWwTUJ0eepDTzZg2d0csvU+xxPmXAlegeE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\sortable.min.js", "FileLength": 3142, "LastWriteTime": "2025-07-02T05:37:25.4647771+00:00"}, "K3H9thCyscCUfiQ7RbAr8auFO87y0CrD8wocyWzC8Wk=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\0b5pm8t2xd-ap0tuxedpx.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/spinner.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\spinner.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rbrjqej3qc", "Integrity": "6ayPRUlyr2jBIpczN3xpfl2b5cXKoXJ+JKLcEtBHlF4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\spinner.min.js", "FileLength": 3398, "LastWriteTime": "2025-07-02T05:37:25.4708153+00:00"}, "ZAPPoZkkW6Oj2vBkYesE9UNAEDKdR5IvnV2BJQy/dfg=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\ancwpmpcu5-4dvlhbz2ty.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/splitbuttonsbase.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\splitbuttonsbase.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rx6z1naeog", "Integrity": "34qdEC017VJ+cuWFELIFyA5IyRLDmQFb2DoSED+soq4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\splitbuttonsbase.min.js", "FileLength": 1447, "LastWriteTime": "2025-07-02T05:37:25.4768487+00:00"}, "oodC/pS1i7Adex0rRI9FMvKGEUywJted55lqLZTFQQc=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\spxlaub6dv-372y316ixj.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/svgbase.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\svgbase.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v1ed2isd95", "Integrity": "na03nZTJkoL8dxcE4fga3CLmJUfzIoZSS7jeF7VnmGM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\svgbase.min.js", "FileLength": 12720, "LastWriteTime": "2025-07-02T05:37:25.4926882+00:00"}, "Q3Vslh2aJOrVih4LMDTp7e18wEjDwORoEBxmZ50QLBk=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\tt93irvsj4-xyx67ogrhz.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/syncfusion-blazor-base.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\syncfusion-blazor-base.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6dj1fh4u5y", "Integrity": "Sis72rKLvek0CA6Rj9oQCYWDJBf0T9Pa/NCkVtVdHFQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\syncfusion-blazor-base.min.js", "FileLength": 100391, "LastWriteTime": "2025-07-02T05:37:25.5362003+00:00"}, "7NQqW42pxv0FeDYDcpLbcQC1l4W0BjoSvhWYWWYhS7U=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\bqvff7ljya-mtu859f7dq.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/syncfusion-blazor.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\syncfusion-blazor.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7nzkh5iinf", "Integrity": "jYlFG3ZVR8PPDDHes9wcBS3SGqA4oah9ywBQe+lzrkY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\scripts\\syncfusion-blazor.min.js", "FileLength": 986843, "LastWriteTime": "2025-07-02T05:37:26.1604407+00:00"}, "zY67c6cdhnkqX0tJMfmbXB28zNPfVc8kR2Fc2yti0as=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\yvsznq4373-otkjqv6u35.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ogo6wfeumt", "Integrity": "WN2Q/vZfoq5xRsWXt75TGakQiyzjVSx1P2f5h7JrLUQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\bootstrap.css", "FileLength": 476131, "LastWriteTime": "2025-07-02T05:37:26.50704+00:00"}, "LxmU90NGokVcJDuZSVUr+2oKM75uLBl48DrAHL9Hifo=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\xo1my5bok1-da1ikz1x3s.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\bootstrap-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rsjzt06b06", "Integrity": "BVQAu1KY3hXrrbby8EYqt+VMRD7avaj8h6tWvAvw5Fw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\bootstrap-dark.css", "FileLength": 475553, "LastWriteTime": "2025-07-02T05:37:26.8078492+00:00"}, "VXNSqZP658rFO+ulQlJENfNIoy2k52y9zIcvRgzoV6w=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\86a35pd8i9-s7kmwdu5yf.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\bootstrap-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w3bq3jxvqs", "Integrity": "vJm+m8vnw6wPvg4T20NouyddMAylPZ+DGHvAtu96CQ8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\bootstrap-dark-lite.css", "FileLength": 475558, "LastWriteTime": "2025-07-02T05:37:27.0980042+00:00"}, "fRqhmc2LglkbWGojA4UA+yQKRZc6/VbMigDWq42Bkhg=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\8503zsol33-cxbvj0zal6.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\bootstrap-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1zsz9whqk6", "Integrity": "bTaDH194c9dSr12ku1F9CrukPZJmcWM/yKkzZeu6dT4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\bootstrap-lite.css", "FileLength": 476137, "LastWriteTime": "2025-07-02T05:37:27.400697+00:00"}, "B9WUHcoj2E/pEujS7o2V0lg1D9FVruksK0D8SQhYvLI=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\34yiq18zxz-6ijb54a1ch.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap4.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\bootstrap4.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "50tpmzlxn4", "Integrity": "FTGE8d79zaBXHCdU6VOuo7r8fJYsmhDT2LANATOeFYw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\bootstrap4.css", "FileLength": 467065, "LastWriteTime": "2025-07-02T05:37:27.681863+00:00"}, "xKkClWPXMWDTnK4ZWW2BH8e0z4+r4Mt5Uhr9gWe3pCc=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\rugulc3dbo-0fg043zwgm.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap4-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\bootstrap4-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k328nl28v7", "Integrity": "oMedcRbE+j52uv1SxTvs60u8MzO11j56tHjXF0tYpuM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\bootstrap4-lite.css", "FileLength": 467069, "LastWriteTime": "2025-07-02T05:37:28.0766257+00:00"}, "JI3YURGN3mOJr6iIKE1Poovf5uIGSgpr/lOk4zUki6k=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\rt6g31qov9-8bo2y8shfk.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap5.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\bootstrap5.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l7hzp1ncex", "Integrity": "4Q6MksKo0oFqdcP6Jk1CEJp3MWkLS8GWBP2PrtJd+No=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\bootstrap5.css", "FileLength": 474409, "LastWriteTime": "2025-07-02T05:37:25.7156006+00:00"}, "L0qNvKTEfZuEU08x6Eixi3rb8O66q/HwMHwqpY/bF6o=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\16lpm8pp05-c4u2p3fbmr.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap5-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\bootstrap5-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xc7iaby9ab", "Integrity": "wat4Gd4BTTZVoPa3w+I4lpRxiOFO6yzZm/Ne2Ngrki8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\bootstrap5-dark.css", "FileLength": 478261, "LastWriteTime": "2025-07-02T05:37:26.0970935+00:00"}, "ZqEM8jYUDYMfcGRx5xsUwCkLmxScbsuj7lDMgf3DGdA=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\7hpqi5txla-x2qei2lj68.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap5-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\bootstrap5-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rn41heff7o", "Integrity": "1yYYhDotmXsHiP8aXH3cWH4pZ9SQbLnh2ogTSObzmlo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\bootstrap5-dark-lite.css", "FileLength": 478266, "LastWriteTime": "2025-07-02T05:37:26.4497572+00:00"}, "LXhGSVg00LjJyQa89+YjJUBFV7QNa72ok4GkoyapCf4=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\apybc81neu-5n6pwg2q85.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap5-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\bootstrap5-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ssfyeumqpe", "Integrity": "fGpNq80OywoesvLUkEgDS94NEMbniBFJi3eTRIUn0fc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\bootstrap5-lite.css", "FileLength": 474415, "LastWriteTime": "2025-07-02T05:37:26.7643736+00:00"}, "guewnpHR1ASfEyZkVRaIfB2hwQBxbgV8EsLuwx5MSYU=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\uoq7cj833o-4tdxru61he.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap5.3.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\bootstrap5.3.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7n5581fzp1", "Integrity": "0wcd58/g5x5Aes4VMEwtqqPCEimj/50nCv31OHpRlf8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\bootstrap5.3.css", "FileLength": 480211, "LastWriteTime": "2025-07-02T05:37:27.0763909+00:00"}, "bdPq8o3kbGWUoFUfBnKW3mFCCBGMPVgGhD2Fdt1J5FU=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\0ljcaj2py1-v9qfcxfxt0.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap5.3-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\bootstrap5.3-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "aefy8ko9dl", "Integrity": "juXqcXN75NnAZpp8a7Y1yM0Hhs3ZYsifMJ8axep+itg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\bootstrap5.3-dark.css", "FileLength": 479301, "LastWriteTime": "2025-07-02T05:37:27.3876127+00:00"}, "Xx+ewYi90AgI4AYn+cfSoZwmngeh5Y7OxMVTa7sQ1VE=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\09tjyrmpyg-p9i9uyxbyk.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap5.3-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\bootstrap5.3-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2whlaspmz7", "Integrity": "0pdqSKyafudh6yX6L8YNMEHXpYQ9siFnx3Se/sqp3sE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\bootstrap5.3-dark-lite.css", "FileLength": 479304, "LastWriteTime": "2025-07-02T05:37:27.6916911+00:00"}, "RpFpmszy9RnApPqRoh75n1weAoQwnI9dOTsHBRAI7Do=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\r3cgco6llb-0az1g3v0c8.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap5.3-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\bootstrap5.3-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wnadw814nc", "Integrity": "PMfxL/TWVG0oTPu9QBAP+7Se/fN4/JvE9iPhXwm8r6Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\bootstrap5.3-lite.css", "FileLength": 480217, "LastWriteTime": "2025-07-02T05:37:28.2076888+00:00"}, "AJUssMAVbDjgJU+4TTGmcYsk7ATcquhHhBjgVRJNHgI=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\3uil544spb-xesxqavj00.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/customized/material.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\customized\\material.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "idppmstdiq", "Integrity": "St3Xs4JsQSC3D6xUSdUuaLcvljgeoUucrrvQrrKaBoY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\customized\\material.css", "FileLength": 504107, "LastWriteTime": "2025-07-02T05:37:28.5096153+00:00"}, "K5yxLPlPLSC7BwpXP4V4sajf7seWcTsT/2cRjRKDKOg=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\0z85kndwvu-46u0nd3byl.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/customized/material-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\customized\\material-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9c1m8you53", "Integrity": "t39T7WBvfrCYGh1spgu9vG/0/YCMThQFLIhOjjdsStM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\customized\\material-dark.css", "FileLength": 510577, "LastWriteTime": "2025-07-02T05:37:28.7927761+00:00"}, "z36cr47my+0gHKUYFUhsPFO6O/IwJXOdX1kmp9mbjk4=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\bxxz62r4fh-4tekdlp3y2.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/customized/tailwind.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\customized\\tailwind.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xufn6cqw00", "Integrity": "9zkWHW4FNM/0XvhknducNZM5WEejH+JH5Cf6ygH2c+8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\customized\\tailwind.css", "FileLength": 438547, "LastWriteTime": "2025-07-02T05:37:29.0516208+00:00"}, "1Boz2aJeE0ymxCLm3Mv/Zm1gR9w9WfeXDwKyBzDiceo=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\97jndmeew1-1kzk8d3a4y.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/customized/tailwind-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\customized\\tailwind-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yeqsaa54nk", "Integrity": "unaaPfySQAsmEEKtLz8iyd2TCuFbM5GUdYYWZmTs5hE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\customized\\tailwind-dark.css", "FileLength": 441526, "LastWriteTime": "2025-07-02T05:37:29.3025467+00:00"}, "issUEuQYugvxcrY8mR+Q3dNokoYaq7nPw/yDFM80hHE=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\tkqxgce19s-sf1x19keag.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fabric.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\fabric.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "arr5fsvjhh", "Integrity": "xioVMcM2LuA5RZYYyzpMGLz+/NDEOAIMbuvh4lNjZJM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\fabric.css", "FileLength": 455590, "LastWriteTime": "2025-07-02T05:37:29.5425854+00:00"}, "OdpX+DwKwxzV4jP80QK5s8KT5mQ2rHpHuV4Uz4fdBwo=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\giwg4o6lvn-b26ojvrkef.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fabric-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\fabric-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "72dvrws58u", "Integrity": "ITEkKGtsgCG9Rz2Q4gCGm0r42rkrZ1Z7DRW7dS6//QU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\fabric-dark.css", "FileLength": 465562, "LastWriteTime": "2025-07-02T05:37:29.7841888+00:00"}, "Fh3bbl0ftXkAmW0WXonnU+QOQouQgoHb6+a93R+pqGs=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\9exgy2h7v5-ihq8qp3om2.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fabric-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\fabric-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8kimz81g9z", "Integrity": "iMXR8M1Prkzeg1EFemsHeSD7w+9Vid2dyAuMrRXggD8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\fabric-dark-lite.css", "FileLength": 465566, "LastWriteTime": "2025-07-02T05:37:30.0334966+00:00"}, "WKHFA7oOIIMedmAcCMdQEcc63MY39W8E3zTQalPw+2o=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\0roaiwm1eq-8xjryt75x9.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fabric-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\fabric-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7bvcujt56", "Integrity": "h4FuFpbeKgXu6/QtUXIDv/an0o6KKwkEOJk3as99aBQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\fabric-lite.css", "FileLength": 455596, "LastWriteTime": "2025-07-02T05:37:30.2821466+00:00"}, "I5pdWcYQ8aVNefz0Kj+4qnfqUgADWXDN6AP5iLTNcS4=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\cpd57ko01h-1836m79nrc.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fluent.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\fluent.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "x3qeui7zc6", "Integrity": "rRHLPTHteN8hcRGw4C2x2tEO1wJ7IMFtc9WKSussdMQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\fluent.css", "FileLength": 454524, "LastWriteTime": "2025-07-02T05:37:30.5466691+00:00"}, "LXA3f87QAwvnX9cFFg1eayVyOk0V9dKYZsvI6Si78tM=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\18b0yyeh6j-5kzff4cv39.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fluent-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\fluent-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qh7mq4ivx0", "Integrity": "kmZxaTX5Yf6GA2/qKFvTuu3ozWjsNHavWveXoMxwjPo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\fluent-dark.css", "FileLength": 454851, "LastWriteTime": "2025-07-02T05:37:26.0950776+00:00"}, "usNui9cTua3F6ZfUihbYo60f2iNJRqFEU+JgMAebVCY=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\r5l6zmsmwz-xkjillxrw3.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fluent-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\fluent-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5p2op5air7", "Integrity": "qTBVskmHxZ5IbFEoBa2dH5g4cHICpQTqgPHrgaA3Bas=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\fluent-dark-lite.css", "FileLength": 454851, "LastWriteTime": "2025-07-02T05:37:26.4537746+00:00"}, "A1o2pKU+jUc345OVDWCMOi0zEmzea0TY5sl7SVFXKPQ=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\w0f18brd7i-92hisotj47.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fluent-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\fluent-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e4g71acazq", "Integrity": "XpYnHbq0wwgSyro2YZMZ9kksp3vuTMULJbORRE9Rrzo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\fluent-lite.css", "FileLength": 454528, "LastWriteTime": "2025-07-02T05:37:26.7565892+00:00"}, "Mt7t0qL1WuZse01QvsxhzDsCuSGKqrb/F4kboIO09wI=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\tzmdu8q96u-t5cex7guuf.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fluent2.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\fluent2.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "satf3egpmj", "Integrity": "7KJbiABRJKHbj+6faCQNtRODO4J2ZKWy/yqp07IOWbI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\fluent2.css", "FileLength": 490238, "LastWriteTime": "2025-07-02T05:37:27.1000108+00:00"}, "O/yKYm4ZJhDj2EBOt8IMxmYWL8izfxXKhFlKLHxSwj4=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\azz7n0gu3n-2t9ms4tcmi.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fluent2-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\fluent2-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g3g686sjsu", "Integrity": "BAlW9qxtCOvuptcZ6/oyWzBBB4TnTXdmQ12pnePhHfE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\fluent2-dark.css", "FileLength": 487229, "LastWriteTime": "2025-07-02T05:37:27.4323557+00:00"}, "0SyC9h9yGbYsPoqdXx11pUiLOXMwHWAfNUXk9TZ/NDU=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\bu4pbrast1-xuetto8xig.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fluent2-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\fluent2-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5h9w3twvia", "Integrity": "+S5OLTR1CDtK22lwcLaOal1oPDyJgabdhGHerQ10WOY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\fluent2-dark-lite.css", "FileLength": 487231, "LastWriteTime": "2025-07-02T05:37:27.7555921+00:00"}, "w5m5/1JmXD311NIX3WwALE14KKjv6KKfX0vXCJk96mM=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\80ppwlqd2m-g3i50uzo9q.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fluent2-highcontrast.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\fluent2-highcontrast.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ycdfast01d", "Integrity": "ngSBmTcyEcqNmv9HDgopKXSd29oedxqBrzz6Zpw5GBw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\fluent2-highcontrast.css", "FileLength": 488741, "LastWriteTime": "2025-07-02T05:37:28.1958745+00:00"}, "zwgCo4QKifWYIVvQTZoQdZBLVyTw0fuB7M4Aacz1Rak=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\dazefiugh9-3hymraaq3i.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fluent2-highcontrast-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\fluent2-highcontrast-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j2o6krkn5t", "Integrity": "pWDdY6wRoB7/rPxGR3J0kJbu/40dYcXne+p0FtI3JhQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\fluent2-highcontrast-lite.css", "FileLength": 488742, "LastWriteTime": "2025-07-02T05:37:28.4964411+00:00"}, "Z88Y40YL8m7qLriAj2d5kEaP7h1bEKmcLCTIElSBFtE=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\2lur8dmyl3-rgdlv06ij8.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fluent2-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\fluent2-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9w70ue7zw7", "Integrity": "+S1ehTo4GyEZ8RsUbCMn+3nr2Xb9UeWQHftW8nA38Oc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\fluent2-lite.css", "FileLength": 490242, "LastWriteTime": "2025-07-02T05:37:28.7947859+00:00"}, "gI+9381WYNfKEI8yLXqaM2gBroHfeqc8n/1XOnWfz4A=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\wb6pcilptw-b4ocx4y4bv.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/highcontrast.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\highcontrast.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "awyfm8cgyd", "Integrity": "luusQZBKy/TSVBjvfP3vU9Rmg5IJzOP/zKszoDfuxjc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\highcontrast.css", "FileLength": 453528, "LastWriteTime": "2025-07-02T05:37:29.0656147+00:00"}, "kn6yXl2pnC/zebdgbOy/mZbR1r6qwFD1NPPmDknKpr4=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\jnn0t5sikh-10r141mz96.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/highcontrast-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\highcontrast-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ysh75zb1qh", "Integrity": "4taLR6wLBkQPKrYdprheawjy3xgjUQtw2uNM5ynV0u0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\highcontrast-lite.css", "FileLength": 453532, "LastWriteTime": "2025-07-02T05:37:29.3213214+00:00"}, "j6mYLp7quJEKxLK0gYoLyB//eKZb1hu4QRvqrirqnqE=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\tz16aznt9x-x8d4ppitx9.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/material.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\material.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u7enn9r0ow", "Integrity": "Rm1TzprVW/Xqa/VP+oYiM5HjQZu2Vi2gdigpA3JWdTk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\material.css", "FileLength": 504157, "LastWriteTime": "2025-07-02T05:37:25.7631275+00:00"}, "ZuCzgJJLRfyIReJsab+8hVJ5T9axt2bGGtAC8J44HYI=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\fr3spr8ava-2hmf80gs3w.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/material-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\material-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "583lszzd9u", "Integrity": "lXuas+VHha0AHqPmagnukH8/6wgsUA7/+nHYOHTY62w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\material-dark.css", "FileLength": 510613, "LastWriteTime": "2025-07-02T05:37:26.178295+00:00"}, "a9DB1WHg12KQuco/5lau7WpnShkBiujWzZKMu+C4WJY=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\vfpw31d6oc-m79jryhlek.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/material-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\material-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7xe8yjprt4", "Integrity": "i9vNZPaxVtsL1vlkX9quN5TMqKyWYs8Qnr//ocUJdGM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\material-dark-lite.css", "FileLength": 510616, "LastWriteTime": "2025-07-02T05:37:26.5346918+00:00"}, "jLfeECnvz+s3wL/4zBhVETZakjDaeaZPN6NYxkVVCrE=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\r3ljs3kien-wdk20m8wam.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/material-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\material-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gy7y5k9cs9", "Integrity": "yOHPfhtH+oMVwg7xf9d/O0nnJxLt+0qxW5g1zxTesHc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\material-lite.css", "FileLength": 504163, "LastWriteTime": "2025-07-02T05:37:26.2042071+00:00"}, "JGdyQ/A7/+alro198PWMQngiPGPu0E/z9WFKj3yhb7M=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\31pjcyl7b8-69c71lqj5p.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/material3.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\material3.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xrzq5n1phm", "Integrity": "Y5c7vDuWo9GWF8Szu1ZK3apL4O7aUTnYHxcKAw2aazk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\material3.css", "FileLength": 456104, "LastWriteTime": "2025-07-02T05:37:26.5424905+00:00"}, "9ZpamW3qPzn3BGJrk6wnLIwoz/uRgdZ4Ga8E2dBwYBQ=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\6k3aze592a-ectdqosaj6.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/material3-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\material3-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7qez458gkr", "Integrity": "sZJe0rwnycjpy+Fpk8Yo5yvJXG3wK2G+DBclAV/yMAk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\material3-dark.css", "FileLength": 455644, "LastWriteTime": "2025-07-02T05:37:26.941615+00:00"}, "5yZyusPP0zFnNB+u6F4A+5N+8wvUbmmDNwNAv7/FEOk=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\6ypvevihw1-twain4oq60.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/material3-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\material3-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wg000crjis", "Integrity": "YCInYPeplRZZ06JAt6R+NUc8BBxRrog0oJHRU3IUy3c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\material3-dark-lite.css", "FileLength": 455651, "LastWriteTime": "2025-07-02T05:37:27.3158303+00:00"}, "CowNy0RscahTWGH61S02XmpXmQDO8omOkE163xV3VYY=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\a06iirivll-xvv2htanqf.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/material3-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\material3-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cujhx3k08y", "Integrity": "TJSogDWNAFLP0xFt1kEO+qca1w6qKe4W3BjYIrw3p7Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\material3-lite.css", "FileLength": 456111, "LastWriteTime": "2025-07-02T05:37:27.7085438+00:00"}, "CiIQ2baxOWBcnGZmOqGvHP6ETSLuTlC1xLaZNKNaKaY=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\6d7zsmg5a9-g01jva92hl.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/tailwind.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\tailwind.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vvh8u0r53t", "Integrity": "i9r7E+G7H1I0XmlH71T+uhgH0c2PTp4X/WtQ4wYTCTg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\tailwind.css", "FileLength": 438616, "LastWriteTime": "2025-07-02T05:37:28.1301421+00:00"}, "OHt55eY6ZWzZpbO1w7aGHXFyGl40cbyMhghHEsy2Zb4=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\iy9vurkk9t-yyhspbp1o2.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/tailwind-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\tailwind-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "eybj5slclb", "Integrity": "RihgOBzf4qQ1Xiy6W5f5IoYWd64EQyIhY30W6V1q//k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\tailwind-dark.css", "FileLength": 441602, "LastWriteTime": "2025-07-02T05:37:26.8518431+00:00"}, "7uxfjJh52zHdimSRH62gdK71MuGQ1zERh8EoBGJ5PeQ=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\hhus5v12qi-mwvd6pk8i0.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/tailwind-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\tailwind-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3p5hyv5uba", "Integrity": "F0XHK/T6Azl1qsKpbAekWhxYryjFwGgKnonzyj+O3ec=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\tailwind-dark-lite.css", "FileLength": 441610, "LastWriteTime": "2025-07-02T05:37:27.2219098+00:00"}, "Fn8hFL4zC7GQiLhpe67L/wdvVOj5vDGDzjBYOxxryEE=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\u360ovkwm8-2hecvjsdbn.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/tailwind-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\tailwind-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dw49makaib", "Integrity": "DX/OxBlIuUQzGT426ip+i4s3JdPCRnXlh72ckpPqbak=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\tailwind-lite.css", "FileLength": 438619, "LastWriteTime": "2025-07-02T05:37:27.5768469+00:00"}, "Hd+Ksw4De/T9ff6GJ0y67YR4GVSlnu9miTkhV30QDOc=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\clkons3prr-hzjg487txq.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/tailwind3.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\tailwind3.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5rhgdmkumq", "Integrity": "4cLZamXUivZmhnFx5i8X9vs194Ht+qCOHT+eE/7Ldsc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\tailwind3.css", "FileLength": 498999, "LastWriteTime": "2025-07-02T05:37:27.9816958+00:00"}, "O0YpQl+rAV+Eol6VNA3qLyjPvubdYavliCpo7KzbCC4=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\6zteovpk60-5m8vl9oe2v.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/tailwind3-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\tailwind3-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ynh6ly6s5w", "Integrity": "EgR5BU2R4OLRGbyxNc3wqHYthuTpoE4OtTV224WPjjk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\tailwind3-dark.css", "FileLength": 496817, "LastWriteTime": "2025-07-02T05:37:28.2885095+00:00"}, "B2TePji/Vu/hQKXdeIY74yMKIUOxoLKEXewZfffq1Yc=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\awup78ll1e-vh74rx7xtg.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/tailwind3-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\tailwind3-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xmgynvhkaz", "Integrity": "RxTNSGwVGvnbzFd3V7NhdOz63dfRwxdKDLjo9IoAUW8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\tailwind3-dark-lite.css", "FileLength": 496820, "LastWriteTime": "2025-07-02T05:37:28.5948909+00:00"}, "CHMApOywvUhE0ENplbY87Su3oELBKWLaRhZD22hcpBw=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\9zslhy3vwt-mjvqjfbph1.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/tailwind3-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\tailwind3-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rxyj1sox8g", "Integrity": "YXem+cBIeeOi9IGVVRLRrNyo5rsjYED0NSTKfjCBB1A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.38\\staticwebassets\\styles\\tailwind3-lite.css", "FileLength": 499003, "LastWriteTime": "2025-07-02T05:37:28.8893967+00:00"}, "Jcg7yPaDj9IzYB84AL3BScqvzIFvQdeth3jBqeTOX6Y=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\cdrmjjq1d6-dpoev2zj9b.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Anchor/FluentAnchor.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Anchor\\FluentAnchor.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fo9kun7a2u", "Integrity": "//clW2GxYKMKCQ11fp3GeXX3FIThT7MZFC8x35s69eU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Anchor\\FluentAnchor.razor.js", "FileLength": 267, "LastWriteTime": "2025-07-02T05:37:28.8929156+00:00"}, "b8c4f9/QrvV8hB5HypSc9DiOzQG7o3JWPd1x47AzF/0=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\twd6d0wh4o-819w3ybe2d.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/AnchoredRegion/FluentAnchoredRegion.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\AnchoredRegion\\FluentAnchoredRegion.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "182vbtilql", "Integrity": "Afys6mc7pO9MdyokTQwtIDBaLTcYTTR9veWNwNIktvU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\AnchoredRegion\\FluentAnchoredRegion.razor.js", "FileLength": 1002, "LastWriteTime": "2025-07-02T05:37:28.8949257+00:00"}, "Fz9qDS4ObOtoVjy7q3nqo4trdXxtWbxZ8/6q8B64PTw=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\akzl5bxou0-p6kf5zqzit.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Button/FluentButton.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Button\\FluentButton.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cxryly5d9i", "Integrity": "ILEdH4KtR1Hb7JiJVs56TK2orcZyV4zmIUMwLCiQMaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Button\\FluentButton.razor.js", "FileLength": 311, "LastWriteTime": "2025-07-02T05:37:28.8969379+00:00"}, "s23vQ5b8jOR1PO8yiqqF4tsCFR6NyQsO7Rf0VMA6gvU=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\cq4bmj8gmb-zjzit57lox.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Checkbox/FluentCheckbox.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Checkbox\\FluentCheckbox.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0aw64dy5f3", "Integrity": "GN9Xra9rtwmLDCgEbgg9cmd8TqFT2hcKzoh25cmNDXU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Checkbox\\FluentCheckbox.razor.js", "FileLength": 217, "LastWriteTime": "2025-07-02T05:37:28.9009632+00:00"}, "ZhqKMZU3IjQzUsS/WTRMQHW5pgDxJwp0YM7Mg1VZbqU=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\klh9ieqob8-nfhyg6xvey.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/DataGrid/FluentDataGrid.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DataGrid\\FluentDataGrid.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7c22o04ryb", "Integrity": "qW8M83Rbn9fAXv7nGXmChE7Qo2htZhG8ALruzktPsU8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DataGrid\\FluentDataGrid.razor.js", "FileLength": 2962, "LastWriteTime": "2025-07-02T05:37:28.9029749+00:00"}, "Ul3F8d638IoXrB6rTrBhO8ykY0IkLuiAjouI+rXn1AQ=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\4jvn6qpxj5-vyjqmndgy2.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/DesignSystemProvider/FluentDesignTheme.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DesignSystemProvider\\FluentDesignTheme.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bo7nvuwvlw", "Integrity": "m/2KN+FpPqIvh9wR4E9l87AbfY4hA5AtyFeDpfKxDbc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DesignSystemProvider\\FluentDesignTheme.razor.js", "FileLength": 754, "LastWriteTime": "2025-07-02T05:37:28.9069993+00:00"}, "PT2oC/2DuthlrR5jerdCkCRYPKz/hOcBXcvacZh4c7g=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\7g337ulk4n-iy34mpf72d.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Divider/FluentDivider.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Divider\\FluentDivider.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "glesyzohpx", "Integrity": "5MY4SrjNix5pk9Q5gpyB70xS5vpChA2Ji/0LnyzJwjo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Divider\\FluentDivider.razor.js", "FileLength": 231, "LastWriteTime": "2025-07-02T05:37:28.9087556+00:00"}, "nKsHw4PP/jzweORVF8hHlEWcqkAT0Oa2HjI+sikHx3A=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\6mwvvlkmgj-hi1gwvth64.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Grid/FluentGrid.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Grid\\FluentGrid.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9zocec3c57", "Integrity": "i0j3kxAp406ufS3MsxTNvjzVgyRqDwIajgOehqvtu6M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Grid\\FluentGrid.razor.js", "FileLength": 759, "LastWriteTime": "2025-07-02T05:37:28.9127969+00:00"}, "aOi/yAFGNUnwSB0vDeNHJxjW1Szv0nDMhn3tTmfBBjw=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\pfc9to5k3q-5pcucyxosc.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/HorizontalScroll/FluentHorizontalScroll.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\HorizontalScroll\\FluentHorizontalScroll.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o73xmx6c30", "Integrity": "hOhymz8Hwrl2LWr7Y0TXXQPcQmpfSCsFogOb6Zv2kB4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\HorizontalScroll\\FluentHorizontalScroll.razor.js", "FileLength": 138, "LastWriteTime": "2025-07-02T05:37:28.9148091+00:00"}, "OQXBycZA41Dd3MSw9krNfTQGNmDtKFm0D6bK0Vyjc5I=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\h0qxm5u0yy-vjluklws0l.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/InputFile/FluentInputFile.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\InputFile\\FluentInputFile.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1sdzkmuo8x", "Integrity": "gbwptaJwa99wlBJATqA39Wvd+hPAzyivnXV9kSwBbNk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\InputFile\\FluentInputFile.razor.js", "FileLength": 834, "LastWriteTime": "2025-07-02T05:37:28.9168192+00:00"}, "xKrHuY0vTtBBl8T5WcycpgdTSt/q9+UPNLecj3gV6x0=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\win2gt64sq-pu9hn1jugj.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/KeyCode/FluentKeyCode.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\KeyCode\\FluentKeyCode.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f0dloqnx2v", "Integrity": "4VrmdaAeF/CVMJ7a55YiF2hEI0LDZi84KcPQcvi3ZM4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\KeyCode\\FluentKeyCode.razor.js", "FileLength": 891, "LastWriteTime": "2025-07-02T05:37:28.9188299+00:00"}, "FlFzG6K74+ARIufehb1tA2Uh7ZiIJLVimE2snyvAMDA=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\ijuf810ucb-xp2f0e0rh3.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Label/FluentInputLabel.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Label\\FluentInputLabel.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nmysnspdoa", "Integrity": "T9UdQlDeqtgxKoaOrC15cRB5b8Xw+a6l3AS6Y+XsXL8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Label\\FluentInputLabel.razor.js", "FileLength": 251, "LastWriteTime": "2025-07-02T05:37:28.9208406+00:00"}, "AthBL+KGq/IXWkgfC/YNrUab47kvskrue4oU16LzETg=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\6o9gthdpke-psptt994gq.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/List/FluentAutocomplete.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentAutocomplete.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b9j8p69emz", "Integrity": "gUtYatED8B/J0nKbZybxmXWnL/UJrKEPtENtEKPigwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentAutocomplete.razor.js", "FileLength": 570, "LastWriteTime": "2025-07-02T05:37:25.3392972+00:00"}, "5pdlEZY+sEwkTHCnjjWyB25UlJnMj9s9QV0SE8zW868=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\tb98bamshq-afevzs963z.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/List/FluentCombobox.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentCombobox.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "inru90os05", "Integrity": "fhAJBRWOqIUuZBy3gnCpMXRnHWRrtx32KmxhrDCzqLQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentCombobox.razor.js", "FileLength": 544, "LastWriteTime": "2025-07-02T05:37:25.3526574+00:00"}, "rkUFmygSv0Gy8jd5cnLRhEIsfGz56zqvcpJURs8CTok=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\ffdjjohsnc-mmp1yy7un5.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/List/ListComponentBase.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\ListComponentBase.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k1s7y80tos", "Integrity": "uEs3SkTKioIAKwT/E2rYx8lRyA3csV0YWEB5VboYALI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\ListComponentBase.razor.js", "FileLength": 137, "LastWriteTime": "2025-07-02T05:37:25.3546685+00:00"}, "nt4xsLUOB66kQAdrnFx82Boa3S2lkoHafU/IdGu+d9w=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\pn46o4hdgm-5wrroj4j54.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Menu/FluentMenu.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Menu\\FluentMenu.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7qvv8cqi0", "Integrity": "aXVPltYYB1IqzLOOo/ihlIm1QLjjfas+mNS4YtuShCI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Menu\\FluentMenu.razor.js", "FileLength": 298, "LastWriteTime": "2025-07-02T05:37:25.3586955+00:00"}, "TxaTb679yh52mPiPQBLiLkkM4CzbxClPzpluiyZnq4s=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\4sjayj892b-9fmja7pljs.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/NavMenu/FluentNavMenu.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\NavMenu\\FluentNavMenu.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ch14addp29", "Integrity": "gI4r2CiVSDsh0E4W8EzzQkWVMP1z5KqWsHc5UHSf4lA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\NavMenu\\FluentNavMenu.razor.js", "FileLength": 1012, "LastWriteTime": "2025-07-02T05:37:25.3607066+00:00"}, "HS+Fkfz1TgkJJcWSxO1ijYuyzMU5VhObvSme3JCyfRU=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\mm8wr5jb95-rgycuwl3sw.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Overflow/FluentOverflow.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overflow\\FluentOverflow.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "88o5cg3t4w", "Integrity": "GLrvqsihiaOKSTpR4HUdwLbyE5ovO/PgdDHABPeQqQ4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overflow\\FluentOverflow.razor.js", "FileLength": 1755, "LastWriteTime": "2025-07-02T05:37:25.3667431+00:00"}, "2WM/gTRSyR94VHmefC3VekgKXN+PnHuSUPKoBYGtwGo=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\wmgko3ir4p-kjm33rwg1a.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Overlay/FluentOverlay.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overlay\\FluentOverlay.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s2s2rdqeh5", "Integrity": "XJbbN4OuwYXj7k13JHIv9Lj1t118BSzODet/C6GywE0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overlay\\FluentOverlay.razor.js", "FileLength": 656, "LastWriteTime": "2025-07-02T05:37:25.3725232+00:00"}, "k2cry5f7hwtA/knIq967Mn6OxsqA0NJGK5hWZ2KgxeA=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\jq2jihf5fr-awzanx0pu8.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/PullToRefresh/FluentPullToRefresh.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\PullToRefresh\\FluentPullToRefresh.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m5zicgu0uv", "Integrity": "ei4JgX2LTuVjcaPCISvNeX1hHKeJ8uYfuz0+kGtJAkI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\PullToRefresh\\FluentPullToRefresh.razor.js", "FileLength": 1844, "LastWriteTime": "2025-07-02T05:37:25.3760557+00:00"}, "LOIXFOTEQIrTZJxsy8yOpdgSSwIOEOTrRSYy0boUjOY=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\uslingtyva-m0sdc2vg34.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Search/FluentSearch.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Search\\FluentSearch.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w98asdg00m", "Integrity": "ZDiIU0h55N8SSpI62bLHZqaEzTK3XYMZ7oWs+4kRUmo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Search\\FluentSearch.razor.js", "FileLength": 327, "LastWriteTime": "2025-07-02T05:37:25.3800819+00:00"}, "IucR6FKsCF3c3yCdQOCIGKlNBT+snkR1woiU+yc1khI=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\2b4v2q74ar-0b0bj86z40.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Slider/FluentSlider.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSlider.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "azl6ax9okv", "Integrity": "Ok3+UlpFQuzZXWBv0rF9abeEFcHU9G9/yGNlsDrEjXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSlider.razor.js", "FileLength": 284, "LastWriteTime": "2025-07-02T05:37:25.3820963+00:00"}, "+LVSNp/p8Yf/yRfDgOhztxf4Wep8xKFqQZarWzOpYNM=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\5eye7ha8pe-e5lgg05xwp.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Slider/FluentSliderLabel.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSliderLabel.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k78ptnt0os", "Integrity": "MEEF9qZp8vzu++12GS1Nrm9HsAwBidvWQdUYcJ+WJvY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSliderLabel.razor.js", "FileLength": 219, "LastWriteTime": "2025-07-02T05:37:25.3861191+00:00"}, "6/u1hQWRbChcrM+axq3USkqaIaR8Vt0oyUXCLV3c8Ko=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\fiqznfocyn-ki10xp5gks.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/SortableList/FluentSortableList.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\SortableList\\FluentSortableList.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tkabjbg0r", "Integrity": "2E4hhc6jOpgtINxrSRMKIqw+DQLQ2SftfcX1ThOrepM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\SortableList\\FluentSortableList.razor.js", "FileLength": 469, "LastWriteTime": "2025-07-02T05:37:25.3901473+00:00"}, "e243bCS6t78vuU/E5meT9HaMR5wl11Rucf6X3bxbrPE=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\5dcvuemm5a-s9hcthfn4x.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Splitter/FluentMultiSplitter.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Splitter\\FluentMultiSplitter.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mxbb0zqj51", "Integrity": "1iL0Rcjo40DSIIzfew0csjsBypwBYzFxskd2cyvll1c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Splitter\\FluentMultiSplitter.razor.js", "FileLength": 1349, "LastWriteTime": "2025-07-02T05:37:25.3961872+00:00"}, "2jByFq2QDRuOhnmdf0BiaQnxTVR6FNLRRBAZoiBehtA=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\sc7iw8bo2n-idf8r2y2gj.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Tabs/FluentTab.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tabs\\FluentTab.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7vrwdrxykv", "Integrity": "7aHGbSJcSaT5cfjQK+Gyrtoyd3MYNywpo1mPbW862yk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tabs\\FluentTab.razor.js", "FileLength": 293, "LastWriteTime": "2025-07-02T05:37:25.3982004+00:00"}, "xK3dvjXg4OCLbJWKCHm7rob/bWou/PbCCFqcdHz14lI=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\sbcuig79xm-btwuipzwbp.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/TextField/FluentTextField.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\TextField\\FluentTextField.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8ov9jz4ar3", "Integrity": "mCekEzA0zB8lJvQIv6M/2Ndz99hlmD0UE8rEDHgUAps=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\TextField\\FluentTextField.razor.js", "FileLength": 464, "LastWriteTime": "2025-07-02T05:37:25.4080189+00:00"}, "vdKU4XuoduadVgQdWdDLn8v1+mcmjgn6quynK18cfXc=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\713i7dud0v-v95crb0bvb.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Toolbar/FluentToolbar.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Toolbar\\FluentToolbar.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4ao9gsby09", "Integrity": "1OzW47M+BAzhQZXA66lSbRUQZP1ObnRRM4TTFKNDCh4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Toolbar\\FluentToolbar.razor.js", "FileLength": 513, "LastWriteTime": "2025-07-02T05:37:25.412049+00:00"}, "GR7u3hYCXfyfFwxt8G4icaOoIY/3vIVnw4i1+QtLdLA=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\is0nail857-b0dyrub9as.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Tooltip/FluentTooltip.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tooltip\\FluentTooltip.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fg0nienog3", "Integrity": "8ePQzkTFcBOjLSt8PxALnbVMRW1l7AfeMHcGCtC1Jdg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tooltip\\FluentTooltip.razor.js", "FileLength": 276, "LastWriteTime": "2025-07-02T05:37:25.4178365+00:00"}, "caTuwPsCoXVtFK6pxk7MMCPQ4OwSxZ3DR5GoZUSQYS4=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\6nn42apl8y-1dlotxxwer.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "css/reboot.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\css\\reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2cbdkxfmct", "Integrity": "Xl2r/uacHVD+mrIVAVDC5GAc5mMeiGjzQRAeyrzLwzc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\css\\reboot.css", "FileLength": 2181, "LastWriteTime": "2025-07-02T05:37:25.4238813+00:00"}, "ABcYvqzRbE+DJeeyN8qm4E/tnyAXVvCmTzCxdPbjTmk=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\m1yjzwotev-f8c5bd5212.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "js/initializersLoader.webview.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\initializersLoader.webview.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mnaqae6vjn", "Integrity": "juVE/OQUgf6veO9EE5R7w8sUbTnBaMtqSwEftLHbeM4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\initializersLoader.webview.js", "FileLength": 513, "LastWriteTime": "2025-07-02T05:37:25.4316781+00:00"}, "dE1GTDGovJGulf1CRwTF63shS89NfM7c4TVb28+fPws=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\ruk4ibgzyp-t5s4sbrbsi.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "js/loading-theme.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\loading-theme.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rgr9t677zw", "Integrity": "SzheGEdps0ArWi83idGsg6m6WlDMfdv4S5PpD4i+7pE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\loading-theme.js", "FileLength": 1182, "LastWriteTime": "2025-07-02T05:37:25.4397418+00:00"}, "VYMpvIS+7bHaPjMfcvlE40hcPTPWyZ1RzjDYTzJbTgo=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\kwrall9b0s-y92cxfqtgl.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.lib.module.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3a8h9hbomh", "Integrity": "1X6i8A29INvrhpx2iST6R2pGeSEC9jwBm/t4w6I3TyA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "FileLength": 90259, "LastWriteTime": "2025-07-02T05:37:25.4866546+00:00"}, "OWAzEBpvXcXqzH7UXPH6hykQ3dERGdAf6nOhjOkCV9s=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\m3ffqup1h6-kz8gc8cxma.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t8zskhp41d", "Integrity": "k1bvyfht4MPa+2CGIYyjCqpmHAmcIb02LT50Ty0KClk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "FileLength": 568, "LastWriteTime": "2025-07-02T05:37:25.4886648+00:00"}, "vNQH17RpV5YCndXpABDEAGp+f/yjiXsdvqE9NHWs12s=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\3emkb5z5ma-8j22j5h3b2.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dj93cmo8cl", "Integrity": "94gUrH5jYenrkpJAKcu8Rcc6G3T/PvRHDTVVsp6p+lo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "FileLength": 278620, "LastWriteTime": "2025-07-02T05:37:25.7573443+00:00"}, "N87U3zP+nwi3DQL7z4biHZZtwl5PMCsY6+rUGKnpqQo=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\0ezckf4ywq-epgrf5ha49.gz", "SourceId": "ProjectECN", "SourceType": "Discovered", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ProjectECN", "RelativePath": "app#[.{fingerprint=epgrf5ha49}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\wwwroot\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0jtypeltqn", "Integrity": "BPVxeG3qESjELexglz5fdK1AkRnZOY4fYTc9GLTbbUY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\wwwroot\\app.css", "FileLength": 3453, "LastWriteTime": "2025-07-02T06:31:41.5162571+00:00"}, "Agrw7mwpI1tJrLxZUf9Vj5un+/xCEKQZ/EkyoaUEskI=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\jydqpd60gd-a8m5cweeeb.gz", "SourceId": "ProjectECN", "SourceType": "Discovered", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ProjectECN", "RelativePath": "favicon#[.{fingerprint=a8m5cweeeb}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7r53oxhn9o", "Integrity": "40vJMMDtK/I4oykwnLTf7u86lMXa0jAst/FsrWyHFAI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\wwwroot\\favicon.ico", "FileLength": 5357, "LastWriteTime": "2025-07-02T05:37:25.7691803+00:00"}, "aB1kaTyI64dHf5Lg46zsU5BT2WyqD4XUJYap1HJYdsI=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\4evbb2ztlb-uhfllo7vmv.gz", "SourceId": "ProjectECN", "SourceType": "Computed", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ProjectECN", "RelativePath": "ProjectECN.modules.json.gz", "AssetKind": "Build", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\jsmodules\\jsmodules.build.manifest.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pmmudzy78n", "Integrity": "EUcPo74MSmhv/hLMcB1hGxjI+dVgHeDs4j3s9WycJ0A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\jsmodules\\jsmodules.build.manifest.json", "FileLength": 93, "LastWriteTime": "2025-07-02T05:37:25.7711942+00:00"}, "s8EfIr8Zx2JWU+P/0MJDqDKx5nXXs11+F1Ryb1/WAmg=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\5dr5gsymsw-q21vm7bk8w.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v99o1hjjup", "Integrity": "DUYRo3aBXcG5TcO2iCbSCYEify4c77QCT7R4VobttpI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css", "FileLength": 14001, "LastWriteTime": "2025-07-02T05:37:25.8279055+00:00"}, "RTYq/SUq3UTJLfcwhGZKoNaP8OIzncYhwdgi8zMbH4A=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\5dar8t01tt-tnv30r1bl8.gz", "SourceId": "ProjectECN", "SourceType": "Computed", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ProjectECN", "RelativePath": "ProjectECN#[.{fingerprint=tnv30r1bl8}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\scopedcss\\bundle\\ProjectECN.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5p5exba8qm", "Integrity": "p34461m23NS2B9thc/7tvXKzzhTea6UPLFuDEqULSzM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\scopedcss\\bundle\\ProjectECN.styles.css", "FileLength": 109, "LastWriteTime": "2025-07-02T05:37:25.8299167+00:00"}, "apklo1GnTDWzFqg0pQABRHD/Bl8wNy3qOpUHnBISSUA=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\jrqb1xrmuc-n93f71nqya.gz", "SourceId": "ProjectECN", "SourceType": "Discovered", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ProjectECN", "RelativePath": "enc/20250422/2W/Other/124510-X@-20250422-ECI#[.{fingerprint=n93f71nqya}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\wwwroot\\enc\\20250422\\2W\\Other\\<EMAIL>", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fatyxs76rj", "Integrity": "x2mmp2/CRSt9j/o3/O2Rj5/sC4S59pwDfZyd0kaIQV0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\wwwroot\\enc\\20250422\\2W\\Other\\<EMAIL>", "FileLength": 253, "LastWriteTime": "2025-07-02T06:44:43.4478569+00:00"}, "hLR6gyFIXnlMplktSKu1TTcJUTAmUjFDxKsY6NNHKd8=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\fdwm6pntry-z1bex5s6ga.gz", "SourceId": "ProjectECN", "SourceType": "Discovered", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ProjectECN", "RelativePath": "enc/20250422/2W/Other/INDEX_ALL#[.{fingerprint=z1bex5s6ga}]?.TXT.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\wwwroot\\enc\\20250422\\2W\\Other\\INDEX_ALL.TXT", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vqckiv9c5u", "Integrity": "ecSaxg3/Z1b+IC50qSEn7gstHhfmFdjvSxynKdmFRp0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\wwwroot\\enc\\20250422\\2W\\Other\\INDEX_ALL.TXT", "FileLength": 494, "LastWriteTime": "2025-07-02T06:44:43.4488518+00:00"}, "zdiUflCVzXx0gmCSe/ZDEEFZNDXj1bZtT/APl8DwNQo=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\6uc98xdnuk-aubivtnycr.gz", "SourceId": "ProjectECN", "SourceType": "Discovered", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ProjectECN", "RelativePath": "enc/20250422/4W/Other/124510-Y@-20250422-ECI#[.{fingerprint=aubivtnycr}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\wwwroot\\enc\\20250422\\4W\\Other\\<EMAIL>", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgp7pfwtf4", "Integrity": "X+7LiYS6GqutZ2eWnkcGD2vfJNQX2cRT3KWmtbtAYcM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\wwwroot\\enc\\20250422\\4W\\Other\\<EMAIL>", "FileLength": 1164, "LastWriteTime": "2025-07-02T06:44:43.4498566+00:00"}, "oH2k/YmznFNHjZ7/PpSC3dXl7dPK6C1+nseVVc9xVRA=": {"Identity": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\l6ua56l9sm-fei5u6jzl7.gz", "SourceId": "ProjectECN", "SourceType": "Discovered", "ContentRoot": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ProjectECN", "RelativePath": "enc/20250422/4W/Other/INDEX_ALL#[.{fingerprint=fei5u6jzl7}]?.TXT.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\wwwroot\\enc\\20250422\\4W\\Other\\INDEX_ALL.TXT", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f65aiozxdr", "Integrity": "t1pCeCoESTcDZ3iWT+q/a/qy1V4aaoJwtMIj+rDeZhY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\OfficeProjects\\ProjectECN\\ProjectECN\\wwwroot\\enc\\20250422\\4W\\Other\\INDEX_ALL.TXT", "FileLength": 827, "LastWriteTime": "2025-07-02T06:44:43.451854+00:00"}}, "CachedCopyCandidates": {}}