# PowerShell script to create a test TIFF image
Add-Type -AssemblyName System.Drawing

# Create a bitmap with sufficient size for our extraction areas
$width = 2000
$height = 1500
$bitmap = New-Object System.Drawing.Bitmap($width, $height)
$graphics = [System.Drawing.Graphics]::FromImage($bitmap)

# Fill with white background
$whiteBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
$graphics.FillRectangle($whiteBrush, 0, 0, $width, $height)

# Create ECN Description Box area (415px, 265px, 1295px wide, 610px height)
$descriptionBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::LightBlue)
$graphics.FillRectangle($descriptionBrush, 415, 265, 1295, 610)

# Add text to description area
$font = New-Object System.Drawing.Font("Arial", 24)
$blackBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::Black)
$graphics.DrawString("ECN DESCRIPTION BOX", $font, $blackBrush, 500, 400)
$graphics.DrawString("52U-0270", $font, $blackBrush, 500, 450)

# Create ECN Factory Box area (1456px, 877px, 254px wide, 360px height)
$factoryBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::LightGreen)
$graphics.FillRectangle($factoryBrush, 1456, 877, 254, 360)

# Add text to factory area
$graphics.DrawString("FACTORY", $font, $blackBrush, 1470, 950)
$graphics.DrawString("BOX", $font, $blackBrush, 1470, 1000)

# Save as TIFF
$outputPath = "test-files\20250702\4W\Other\ECN\52U-0270_124510.tif"
$bitmap.Save($outputPath, [System.Drawing.Imaging.ImageFormat]::Tiff)

Write-Host "Test TIFF created: $outputPath"

# Cleanup
$graphics.Dispose()
$bitmap.Dispose()
$whiteBrush.Dispose()
$descriptionBrush.Dispose()
$factoryBrush.Dispose()
$blackBrush.Dispose()
$font.Dispose()
