using System.IO.Compression;
using Microsoft.AspNetCore.Components.Forms;

namespace ProjectECN.Services
{
    public class ECNUploadService
    {
        private readonly IWebHostEnvironment _environment;
        private readonly ILogger<ECNUploadService> _logger;

        public ECNUploadService(IWebHostEnvironment environment, ILogger<ECNUploadService> logger)
        {
            _environment = environment;
            _logger = logger;
        }

        public async Task<UploadResult> UploadAndExtractECNFileAsync(IBrowserFile file, IProgress<UploadProgress>? progress, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation($"Starting upload process for file: {file.Name}, Size: {file.Size}");

                // Validate file name format (YYYYMMDD.zip)
                var fileName = file.Name;
                if (!IsValidECNFileName(fileName))
                {
                    _logger.LogWarning($"Invalid file name format: {fileName}");
                    return new UploadResult { Success = false, ErrorMessage = "Invalid file name format. Expected format: YYYYMMDD.zip" };
                }

                progress?.Report(new UploadProgress { Stage = "Validating", Percentage = 10 });
                cancellationToken.ThrowIfCancellationRequested();

                // Check if file already exists
                var zipFolderPath = Path.Combine(_environment.WebRootPath, "enc-zip");
                var zipFilePath = Path.Combine(zipFolderPath, fileName);

                _logger.LogInformation($"Zip folder path: {zipFolderPath}");
                _logger.LogInformation($"Zip file path: {zipFilePath}");

                if (File.Exists(zipFilePath))
                {
                    _logger.LogWarning($"File already exists: {zipFilePath}");
                    return new UploadResult { Success = false, ErrorMessage = "File with the same name already exists." };
                }

                progress?.Report(new UploadProgress { Stage = "Uploading", Percentage = 20 });
                cancellationToken.ThrowIfCancellationRequested();

                // Ensure directories exist
                Directory.CreateDirectory(zipFolderPath);
                var extractFolderPath = Path.Combine(_environment.WebRootPath, "enc");
                Directory.CreateDirectory(extractFolderPath);

                _logger.LogInformation($"Created directories - Zip: {zipFolderPath}, Extract: {extractFolderPath}");

                // Upload file
                _logger.LogInformation($"Starting file upload to: {zipFilePath}");
                using (var fileStream = new FileStream(zipFilePath, FileMode.Create))
                {
                    await file.OpenReadStream(maxAllowedSize: 100 * 1024 * 1024) // 100MB limit
                        .CopyToAsync(fileStream, cancellationToken);
                }

                _logger.LogInformation($"File uploaded successfully. File size on disk: {new FileInfo(zipFilePath).Length}");

                progress?.Report(new UploadProgress { Stage = "Extracting", Percentage = 60 });
                cancellationToken.ThrowIfCancellationRequested();

                // Extract ZIP file
                _logger.LogInformation($"Starting extraction to: {extractFolderPath}");
                await ExtractZipFileAsync(zipFilePath, extractFolderPath, progress, cancellationToken);

                progress?.Report(new UploadProgress { Stage = "Cleaning up", Percentage = 90 });
                cancellationToken.ThrowIfCancellationRequested();

                // Delete the uploaded ZIP file
                _logger.LogInformation($"Deleting ZIP file: {zipFilePath}");
                File.Delete(zipFilePath);

                progress?.Report(new UploadProgress { Stage = "Complete", Percentage = 100 });

                _logger.LogInformation($"Successfully processed ECN file: {fileName}");
                return new UploadResult { Success = true, Message = $"Successfully uploaded and extracted {fileName}" };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error processing ECN file: {file.Name}");
                return new UploadResult { Success = false, ErrorMessage = $"Error processing file: {ex.Message}" };
            }
        }

        private bool IsValidECNFileName(string fileName)
        {
            if (string.IsNullOrEmpty(fileName) || !fileName.EndsWith(".zip", StringComparison.OrdinalIgnoreCase))
                return false;

            var nameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
            
            // Check if it's exactly 8 digits (YYYYMMDD)
            if (nameWithoutExtension.Length != 8)
                return false;

            // Check if all characters are digits
            if (!nameWithoutExtension.All(char.IsDigit))
                return false;

            // Validate date format
            if (DateTime.TryParseExact(nameWithoutExtension, "yyyyMMdd", null, System.Globalization.DateTimeStyles.None, out _))
                return true;

            return false;
        }

        private async Task ExtractZipFileAsync(string zipFilePath, string extractPath, IProgress<UploadProgress>? progress, CancellationToken cancellationToken = default)
        {
            await Task.Run(() =>
            {
                using (var archive = ZipFile.OpenRead(zipFilePath))
                {
                    var totalEntries = archive.Entries.Count;
                    var processedEntries = 0;

                    foreach (var entry in archive.Entries)
                    {
                        cancellationToken.ThrowIfCancellationRequested();

                        // Skip directories
                        if (string.IsNullOrEmpty(entry.Name))
                            continue;

                        var destinationPath = Path.Combine(extractPath, entry.FullName);

                        // Ensure the directory exists
                        var directoryPath = Path.GetDirectoryName(destinationPath);
                        if (!string.IsNullOrEmpty(directoryPath))
                        {
                            Directory.CreateDirectory(directoryPath);
                        }

                        // Extract the file
                        entry.ExtractToFile(destinationPath, overwrite: true);

                        processedEntries++;
                        var extractProgress = 60 + (int)((double)processedEntries / totalEntries * 30);
                        progress?.Report(new UploadProgress { Stage = "Extracting", Percentage = extractProgress });
                    }
                }
            }, cancellationToken);
        }
    }

    public class UploadResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
    }

    public class UploadProgress
    {
        public string Stage { get; set; } = string.Empty;
        public int Percentage { get; set; }
    }
}
