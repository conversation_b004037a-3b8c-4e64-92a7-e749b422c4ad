﻿@page "/ecn/upload"
@rendermode InteractiveServer
@using ProjectECN.Services
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.JSInterop
@inject ECNUploadService UploadService
@inject ECNImageProcessingService ImageProcessingService
@inject IJSRuntime JSRuntime
@implements IAsyncDisposable

<div class="ecn-upload-container">
    <div class="upload-header">
        <h2>📁 Engineering Change Notice Upload</h2>
        <p class="upload-description">Upload daily ECN files in YYYYMMDD.zip format</p>
    </div>

    <div class="upload-card">
        @if (!isUploading)
        {
            <div class="file-input-section">
                <h3>Select ECN File</h3>
                <p class="file-format-info">Accepted format: YYYYMMDD.zip (e.g., 20250702.zip)</p>

                <InputFile @ref="fileInput"
                          accept=".zip"
                          OnChange="HandleFileSelected"
                          class="file-input" />

                <button type="button"
                        class="browse-button"
                        @onclick="OpenFileDialog">
                    📂 Browse Files
                </button>
            </div>

            @if (selectedFile != null)
            {
                <div class="selected-file-info">
                    <div class="file-icon">📄</div>
                    <div class="file-details">
                        <strong>@selectedFile.Name</strong>
                        <span class="file-size">@FormatFileSize(selectedFile.Size)</span>
                    </div>
                    <button type="button"
                            class="upload-button"
                            @onclick="StartUpload">
                        ⬆️ Upload & Extract
                    </button>
                </div>
            }
        }
        else
        {
            <div class="upload-progress-container">
                <div class="progress-header">
                    <h3>🔄 Processing ECN File</h3>
                </div>

                <div class="progress-details">
                    <div class="current-stage">
                        <strong>Current Stage:</strong> @currentProgress.Stage
                    </div>

                    <div class="progress-bar-container">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: @(currentProgress.Percentage)%"></div>
                        </div>
                        <div class="progress-percentage">
                            @currentProgress.Percentage%
                        </div>
                    </div>
                </div>

                @if (!string.IsNullOrEmpty(selectedFile?.Name))
                {
                    <div class="processing-file">
                        Processing: <strong>@selectedFile.Name</strong>
                    </div>
                }

                <div class="upload-actions">
                    <button type="button"
                            class="cancel-button"
                            @onclick="CancelUpload">
                        ❌ Cancel Upload
                    </button>
                </div>
            </div>
        }

        @if (!string.IsNullOrEmpty(resultMessage))
        {
            <div class="message-bar @(isSuccess ? "success" : "error")">
                @resultMessage
            </div>
        }
    </div>

    <div class="upload-instructions">
        <h4>Upload Instructions:</h4>
        <ul>
            <li>File must be in ZIP format with name pattern YYYYMMDD.zip</li>
            <li>Files with duplicate names will be rejected</li>
            <li>Maximum file size: 100MB</li>
            <li>Files will be extracted to the ECN folder automatically</li>
            <li>TIFF images will be processed to extract ECN Description and Factory boxes</li>
            <li>Extracted image portions will be saved as PNG files in the same folders</li>
            <li>Original ZIP file will be deleted after successful processing</li>
        </ul>
    </div>
</div>

@code {
    private InputFile? fileInput;
    private IBrowserFile? selectedFile;
    private bool isUploading = false;
    private string resultMessage = string.Empty;
    private bool isSuccess = false;
    private UploadProgress currentProgress = new();
    private CancellationTokenSource? uploadCancellationTokenSource;
    private bool isDisposed = false;

    private Task HandleFileSelected(InputFileChangeEventArgs e)
    {
        Console.WriteLine($"File selected: {e.File?.Name}, Size: {e.File?.Size}");
        selectedFile = e.File;
        resultMessage = string.Empty;
        StateHasChanged();
        return Task.CompletedTask;
    }

    private async Task OpenFileDialog()
    {
        if (isDisposed)
            return;

        try
        {
            Console.WriteLine("Opening file dialog...");
            await JSRuntime.InvokeVoidAsync("eval", "document.querySelector('.file-input').click()");
        }
        catch (JSDisconnectedException)
        {
            Console.WriteLine("Circuit disconnected during file dialog open");
            // Circuit has been disconnected, ignore the error
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error opening file dialog: {ex}");
            resultMessage = $"Error opening file dialog: {ex.Message}";
            isSuccess = false;
            StateHasChanged();
        }
    }

    private async Task StartUpload()
    {
        if (selectedFile == null || isDisposed)
        {
            resultMessage = "No file selected.";
            isSuccess = false;
            StateHasChanged();
            return;
        }

        // Cancel any existing upload
        uploadCancellationTokenSource?.Cancel();
        uploadCancellationTokenSource = new CancellationTokenSource();

        isUploading = true;
        resultMessage = string.Empty;
        currentProgress = new UploadProgress { Stage = "Starting", Percentage = 0 };
        StateHasChanged();

        var progress = new Progress<UploadProgress>(p =>
        {
            if (!isDisposed)
            {
                currentProgress = p;
                InvokeAsync(StateHasChanged);
            }
        });

        try
        {
            Console.WriteLine($"Starting upload for file: {selectedFile.Name}, Size: {selectedFile.Size}");
            var result = await UploadService.UploadAndExtractECNFileAsync(selectedFile, progress, uploadCancellationTokenSource.Token);
            Console.WriteLine($"Upload result: Success={result.Success}, Message={result.Message}, Error={result.ErrorMessage}");

            if (!isDisposed)
            {
                isSuccess = result.Success;
                resultMessage = result.Success ? result.Message : result.ErrorMessage;

                if (result.Success)
                {
                    selectedFile = null;
                }
            }
        }
        catch (OperationCanceledException)
        {
            Console.WriteLine("Upload was cancelled");
            if (!isDisposed)
            {
                isSuccess = false;
                resultMessage = "Upload was cancelled.";
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Upload failed with exception: {ex}");
            if (!isDisposed)
            {
                isSuccess = false;
                resultMessage = $"Upload failed: {ex.Message}";
            }
        }
        finally
        {
            if (!isDisposed)
            {
                isUploading = false;
                StateHasChanged();
            }
        }
    }

    private void CancelUpload()
    {
        uploadCancellationTokenSource?.Cancel();
        isUploading = false;
        resultMessage = "Upload cancelled by user.";
        isSuccess = false;
        StateHasChanged();
    }

    private string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }

    public async ValueTask DisposeAsync()
    {
        isDisposed = true;

        // Cancel any ongoing upload
        uploadCancellationTokenSource?.Cancel();
        uploadCancellationTokenSource?.Dispose();

        // Clear references
        selectedFile = null;
        fileInput = null;
    }
}
