﻿@page "/ecn/upload"
@rendermode InteractiveServer
@using ProjectECN.Services
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.JSInterop
@inject ECNUploadService UploadService
@inject IJSRuntime JSRuntime
@implements IAsyncDisposable

<div class="ecn-upload-container">
    <div class="upload-header">
        <FluentIcon Value="@(new Icons.Regular.Size24.CloudAdd())" Color="@Color.Accent" />
        <h2>Engineering Change Note Upload</h2>
        <p class="upload-description">Upload daily ECN files in YYYYMMDD.zip format</p>
    </div>

    <div class="upload-card">
        @if (!isUploading)
        {
            <div class="file-drop-zone @(isDragOver ? "drag-over" : "")"
                 @ondragover="HandleDragOver"
                 @ondragover:preventDefault="true"
                 @ondragleave="HandleDragLeave"
                 @ondrop="HandleDrop"
                 @ondrop:preventDefault="true">

                <FluentIcon Value="@(new Icons.Regular.Size24.CloudAdd())" Color="@Color.Neutral" />
                <h3>Drop your ECN file here</h3>
                <p>or click to browse</p>
                <p class="file-format-info">Accepted format: YYYYMMDD.zip (e.g., 20250702.zip)</p>

                <FluentInputFile @ref="fileInput"
                                 Accept=".zip"
                                 OnChange="HandleFileSelected"
                                 class="hidden-file-input" />

                <FluentButton Appearance="Appearance.Accent"
                              IconStart="@(new Icons.Regular.Size16.Folder())"
                              @onclick="OpenFileDialog">
                    Browse Files
                </FluentButton>
            </div>

            @if (selectedFile != null)
            {
                <div class="selected-file-info">
                    <FluentIcon Value="@(new Icons.Regular.Size20.Document())" Color="@Color.Success" />
                    <div class="file-details">
                        <strong>@selectedFile.Name</strong>
                        <span class="file-size">@FormatFileSize(selectedFile.Size)</span>
                    </div>
                    <FluentButton Appearance="Appearance.Accent"
                                  IconStart="@(new Icons.Regular.Size16.CloudAdd())"
                                  @onclick="StartUpload">
                        Upload & Extract
                    </FluentButton>
                </div>
            }
        }
        else
        {
            <div class="upload-progress-container">
                <div class="progress-header">
                    <FluentIcon Value="@(new Icons.Regular.Size24.ArrowSync())" Color="@Color.Accent" />
                    <h3>Processing ECN File</h3>
                </div>

                <div class="progress-details">
                    <div class="current-stage">
                        <strong>Current Stage:</strong> @currentProgress.Stage
                    </div>

                    <FluentProgress Value="@currentProgress.Percentage"
                                   Max="100"
                                   class="upload-progress-bar" />

                    <div class="progress-percentage">
                        @currentProgress.Percentage%
                    </div>
                </div>

                @if (!string.IsNullOrEmpty(selectedFile?.Name))
                {
                    <div class="processing-file">
                        Processing: <strong>@selectedFile.Name</strong>
                    </div>
                }

                <div class="upload-actions">
                    <FluentButton Appearance="Appearance.Neutral"
                                  IconStart="@(new Icons.Regular.Size16.Dismiss())"
                                  @onclick="CancelUpload">
                        Cancel Upload
                    </FluentButton>
                </div>
            </div>
        }

        @if (!string.IsNullOrEmpty(resultMessage))
        {
            <FluentMessageBar Intent="@(isSuccess ? MessageIntent.Success : MessageIntent.Error)"
                              class="result-message">
                @resultMessage
            </FluentMessageBar>
        }
    </div>

    <div class="upload-instructions">
        <h4>Upload Instructions:</h4>
        <ul>
            <li>File must be in ZIP format with name pattern YYYYMMDD.zip</li>
            <li>Files with duplicate names will be rejected</li>
            <li>Maximum file size: 100MB</li>
            <li>Files will be extracted to the ECN folder automatically</li>
            <li>Original ZIP file will be deleted after successful extraction</li>
        </ul>
    </div>
</div>

@code {
    private FluentInputFile? fileInput;
    private IBrowserFile? selectedFile;
    private bool isUploading = false;
    private bool isDragOver = false;
    private string resultMessage = string.Empty;
    private bool isSuccess = false;
    private UploadProgress currentProgress = new();
    private CancellationTokenSource? uploadCancellationTokenSource;
    private bool isDisposed = false;

    private Task HandleFileSelected(InputFileChangeEventArgs e)
    {
        selectedFile = e.File;
        resultMessage = string.Empty;
        StateHasChanged();
        return Task.CompletedTask;
    }

    private async Task OpenFileDialog()
    {
        if (isDisposed || fileInput?.Element == null)
            return;

        try
        {
            await JSRuntime.InvokeVoidAsync("eval", "document.querySelector('input[type=file]').click()");
        }
        catch (JSDisconnectedException)
        {
            // Circuit has been disconnected, ignore the error
        }
        catch (Exception ex)
        {
            resultMessage = $"Error opening file dialog: {ex.Message}";
            isSuccess = false;
            StateHasChanged();
        }
    }

    private void HandleDragOver(Microsoft.AspNetCore.Components.Web.DragEventArgs e)
    {
        isDragOver = true;
    }

    private void HandleDragLeave(Microsoft.AspNetCore.Components.Web.DragEventArgs e)
    {
        isDragOver = false;
    }

    private void HandleDrop(Microsoft.AspNetCore.Components.Web.DragEventArgs e)
    {
        isDragOver = false;

        // Note: File drop handling through drag and drop is limited in Blazor Server
        // Users should use the file input instead
        resultMessage = "Please use the Browse Files button to select your file.";
        StateHasChanged();
    }

    private async Task StartUpload()
    {
        if (selectedFile == null || isDisposed) return;

        // Cancel any existing upload
        uploadCancellationTokenSource?.Cancel();
        uploadCancellationTokenSource = new CancellationTokenSource();

        isUploading = true;
        resultMessage = string.Empty;
        currentProgress = new UploadProgress { Stage = "Starting", Percentage = 0 };
        StateHasChanged();

        var progress = new Progress<UploadProgress>(p =>
        {
            if (!isDisposed)
            {
                currentProgress = p;
                InvokeAsync(StateHasChanged);
            }
        });

        try
        {
            var result = await UploadService.UploadAndExtractECNFileAsync(selectedFile, progress, uploadCancellationTokenSource.Token);

            if (!isDisposed)
            {
                isSuccess = result.Success;
                resultMessage = result.Success ? result.Message : result.ErrorMessage;

                if (result.Success)
                {
                    selectedFile = null;
                }
            }
        }
        catch (OperationCanceledException)
        {
            if (!isDisposed)
            {
                isSuccess = false;
                resultMessage = "Upload was cancelled.";
            }
        }
        catch (Exception ex)
        {
            if (!isDisposed)
            {
                isSuccess = false;
                resultMessage = $"Upload failed: {ex.Message}";
            }
        }
        finally
        {
            if (!isDisposed)
            {
                isUploading = false;
                StateHasChanged();
            }
        }
    }

    private void CancelUpload()
    {
        uploadCancellationTokenSource?.Cancel();
        isUploading = false;
        resultMessage = "Upload cancelled by user.";
        isSuccess = false;
        StateHasChanged();
    }

    private string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }

    public async ValueTask DisposeAsync()
    {
        isDisposed = true;

        // Cancel any ongoing upload
        uploadCancellationTokenSource?.Cancel();
        uploadCancellationTokenSource?.Dispose();

        // Clear references
        selectedFile = null;
        fileInput = null;
    }
}
